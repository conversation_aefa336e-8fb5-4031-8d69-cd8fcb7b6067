"""
大乐透分析器

实现大乐透彩票的专用分析功能，包括奇偶分析、大小分析、号码遗漏分析、分区比分析。
支持前区（5个号码）和后区（2个号码）的分别分析。
"""

from typing import Dict, List, Any, Tuple
import logging

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from analysis.base import LotteryAnalyzer
from analysis.utils import (
    generate_odd_even_pattern, count_odd_even,
    generate_big_small_pattern, count_big_small,
    generate_zone_ratio_pattern, count_zone_distribution,
    calculate_statistics, format_date_range
)
from data.config import LOTTERY_TYPE_DLT

logger = logging.getLogger(__name__)


class DLTAnalyzer(LotteryAnalyzer):
    """
    大乐透分析器类
    
    专门用于大乐透彩票的数据分析，支持前区后区分别分析。
    """
    
    def __init__(self):
        """初始化大乐透分析器"""
        super().__init__(LOTTERY_TYPE_DLT)
    
    def analyze_odd_even_distribution(self, zone_type: str = 'front') -> Dict[str, Any]:
        """
        分析奇偶分布
        
        Args:
            zone_type (str): 区域类型 ('front' 前区 或 'back' 后区)
            
        Returns:
            Dict[str, Any]: 奇偶分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")
        
        logger.info(f"开始大乐透{zone_type}区奇偶分析")
        
        # 提取对应区域的号码
        if zone_type == 'front':
            numbers_list = [data.get_front_numbers() for data in self.data_list]
            zone_name = "前区"
        elif zone_type == 'back':
            numbers_list = [data.get_back_numbers() for data in self.data_list]
            zone_name = "后区"
        else:
            raise ValueError("zone_type 必须是 'front' 或 'back'")
        
        # 生成奇偶模式列表
        patterns = []
        odd_counts = []
        even_counts = []
        detailed_results = []
        
        for i, numbers in enumerate(numbers_list):
            if not numbers:
                continue
                
            pattern = generate_odd_even_pattern(numbers)
            odd_count, even_count = count_odd_even(numbers)
            
            patterns.append(pattern)
            odd_counts.append(odd_count)
            even_counts.append(even_count)
            
            detailed_results.append({
                'issue_number': self.data_list[i].issue_number,
                'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                'numbers': numbers,
                'pattern': pattern,
                'odd_count': odd_count,
                'even_count': even_count
            })
        
        # 计算模式统计
        pattern_stats = self.calculate_pattern_statistics(patterns)
        
        # 计算奇偶数量统计
        odd_stats = calculate_statistics(odd_counts)
        even_stats = calculate_statistics(even_counts)
        
        # 获取最新的奇偶模式
        latest_pattern = patterns[-1] if patterns else ""
        latest_missing = self.get_missing_periods(patterns, latest_pattern)
        
        result = {
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),
            'date_range': format_date_range(self.data_list[0].draw_date, self.data_list[-1].draw_date),
            
            # 模式统计
            'pattern_statistics': pattern_stats,
            'most_common_patterns': sorted(pattern_stats.items(), 
                                         key=lambda x: x[1]['count'], reverse=True)[:10],
            
            # 奇偶数量统计
            'odd_count_stats': odd_stats,
            'even_count_stats': even_stats,
            
            # 最新状态
            'latest_pattern': latest_pattern,
            'latest_missing_periods': latest_missing,
            
            # 详细结果（最近20期）
            'recent_details': detailed_results[-20:],
            
            # 全部详细结果
            'all_details': detailed_results
        }
        
        logger.info(f"大乐透{zone_name}奇偶分析完成，共分析 {len(patterns)} 期数据")
        return result
    
    def get_odd_even_pattern(self, issue_number: str = None, zone_type: str = 'front') -> str:
        """
        获取指定期号的奇偶模式代码
        
        Args:
            issue_number (str, optional): 期号，如果为None则返回最新期的模式
            zone_type (str): 区域类型 ('front' 或 'back')
            
        Returns:
            str: 奇偶模式代码
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")
        
        # 查找指定期号的数据
        target_data = None
        if issue_number is None:
            target_data = self.data_list[-1]  # 最新期
        else:
            for data in self.data_list:
                if data.issue_number == issue_number:
                    target_data = data
                    break
        
        if target_data is None:
            raise ValueError(f"未找到期号 {issue_number} 的数据")
        
        # 获取对应区域的号码
        if zone_type == 'front':
            numbers = target_data.get_front_numbers()
        elif zone_type == 'back':
            numbers = target_data.get_back_numbers()
        else:
            raise ValueError("zone_type 必须是 'front' 或 'back'")
        
        return generate_odd_even_pattern(numbers)
    
    def calculate_odd_even_intervals(self, target_pattern: str, zone_type: str = 'front') -> Dict[str, Any]:
        """
        计算指定奇偶模式的出现间隔
        
        Args:
            target_pattern (str): 目标奇偶模式（如 "10110"）
            zone_type (str): 区域类型 ('front' 或 'back')
            
        Returns:
            Dict[str, Any]: 间隔分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")
        
        # 获取所有奇偶模式
        if zone_type == 'front':
            numbers_list = [data.get_front_numbers() for data in self.data_list]
            zone_name = "前区"
        elif zone_type == 'back':
            numbers_list = [data.get_back_numbers() for data in self.data_list]
            zone_name = "后区"
        else:
            raise ValueError("zone_type 必须是 'front' 或 'back'")
        
        patterns = [generate_odd_even_pattern(numbers) for numbers in numbers_list if numbers]
        
        # 计算间隔
        intervals = self.find_pattern_intervals(patterns, target_pattern)
        missing_periods = self.get_missing_periods(patterns, target_pattern)
        
        # 查找所有出现的位置和日期
        occurrences = []
        for i, pattern in enumerate(patterns):
            if pattern == target_pattern:
                occurrences.append({
                    'position': i + 1,
                    'issue_number': self.data_list[i].issue_number,
                    'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                    'numbers': numbers_list[i]
                })
        
        # 统计信息
        interval_stats = calculate_statistics(intervals) if intervals else {}
        
        result = {
            'target_pattern': target_pattern,
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),
            
            # 出现统计
            'occurrence_count': len(occurrences),
            'occurrence_frequency': len(occurrences) / len(patterns) if patterns else 0,
            
            # 间隔统计
            'intervals': intervals,
            'interval_statistics': interval_stats,
            'missing_periods': missing_periods,
            
            # 出现详情
            'occurrences': occurrences,
            
            # 最近出现
            'last_occurrence': occurrences[-1] if occurrences else None
        }
        
        return result
    
    def analyze_big_small_distribution(self, zone_type: str = 'front') -> Dict[str, Any]:
        """
        分析大小分布

        Args:
            zone_type (str): 区域类型 ('front' 前区 或 'back' 后区)

        Returns:
            Dict[str, Any]: 大小分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        logger.info(f"开始大乐透{zone_type}区大小分析")

        # 提取对应区域的号码
        if zone_type == 'front':
            numbers_list = [data.get_front_numbers() for data in self.data_list]
            zone_name = "前区"
        elif zone_type == 'back':
            numbers_list = [data.get_back_numbers() for data in self.data_list]
            zone_name = "后区"
        else:
            raise ValueError("zone_type 必须是 'front' 或 'back'")

        # 生成大小模式列表
        patterns = []
        big_counts = []
        small_counts = []
        detailed_results = []

        for i, numbers in enumerate(numbers_list):
            if not numbers:
                continue

            pattern = generate_big_small_pattern(numbers, LOTTERY_TYPE_DLT, zone_type)
            big_count, small_count = count_big_small(numbers, LOTTERY_TYPE_DLT, zone_type)

            patterns.append(pattern)
            big_counts.append(big_count)
            small_counts.append(small_count)

            detailed_results.append({
                'issue_number': self.data_list[i].issue_number,
                'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                'numbers': numbers,
                'pattern': pattern,
                'big_count': big_count,
                'small_count': small_count
            })

        # 计算模式统计
        pattern_stats = self.calculate_pattern_statistics(patterns)

        # 计算大小数量统计
        big_stats = calculate_statistics(big_counts)
        small_stats = calculate_statistics(small_counts)

        # 获取最新的大小模式
        latest_pattern = patterns[-1] if patterns else ""
        latest_missing = self.get_missing_periods(patterns, latest_pattern)

        result = {
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),
            'date_range': format_date_range(self.data_list[0].draw_date, self.data_list[-1].draw_date),

            # 模式统计
            'pattern_statistics': pattern_stats,
            'most_common_patterns': sorted(pattern_stats.items(),
                                         key=lambda x: x[1]['count'], reverse=True)[:10],

            # 大小数量统计
            'big_count_stats': big_stats,
            'small_count_stats': small_stats,

            # 最新状态
            'latest_pattern': latest_pattern,
            'latest_missing_periods': latest_missing,

            # 详细结果（最近20期）
            'recent_details': detailed_results[-20:],

            # 全部详细结果
            'all_details': detailed_results
        }

        logger.info(f"大乐透{zone_name}大小分析完成，共分析 {len(patterns)} 期数据")
        return result

    def get_big_small_pattern(self, issue_number: str = None, zone_type: str = 'front') -> str:
        """
        获取指定期号的大小模式代码

        Args:
            issue_number (str, optional): 期号，如果为None则返回最新期的模式
            zone_type (str): 区域类型 ('front' 或 'back')

        Returns:
            str: 大小模式代码
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        # 查找指定期号的数据
        target_data = None
        if issue_number is None:
            target_data = self.data_list[-1]  # 最新期
        else:
            for data in self.data_list:
                if data.issue_number == issue_number:
                    target_data = data
                    break

        if target_data is None:
            raise ValueError(f"未找到期号 {issue_number} 的数据")

        # 获取对应区域的号码
        if zone_type == 'front':
            numbers = target_data.get_front_numbers()
        elif zone_type == 'back':
            numbers = target_data.get_back_numbers()
        else:
            raise ValueError("zone_type 必须是 'front' 或 'back'")

        return generate_big_small_pattern(numbers, LOTTERY_TYPE_DLT, zone_type)

    def calculate_big_small_intervals(self, target_pattern: str, zone_type: str = 'front') -> Dict[str, Any]:
        """
        计算指定大小模式的出现间隔

        Args:
            target_pattern (str): 目标大小模式（如 "10110"）
            zone_type (str): 区域类型 ('front' 或 'back')

        Returns:
            Dict[str, Any]: 间隔分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        # 获取所有大小模式
        if zone_type == 'front':
            numbers_list = [data.get_front_numbers() for data in self.data_list]
            zone_name = "前区"
        elif zone_type == 'back':
            numbers_list = [data.get_back_numbers() for data in self.data_list]
            zone_name = "后区"
        else:
            raise ValueError("zone_type 必须是 'front' 或 'back'")

        patterns = [generate_big_small_pattern(numbers, LOTTERY_TYPE_DLT, zone_type) for numbers in numbers_list if numbers]

        # 计算间隔
        intervals = self.find_pattern_intervals(patterns, target_pattern)
        missing_periods = self.get_missing_periods(patterns, target_pattern)

        # 查找所有出现的位置和日期
        occurrences = []
        for i, pattern in enumerate(patterns):
            if pattern == target_pattern:
                occurrences.append({
                    'position': i + 1,
                    'issue_number': self.data_list[i].issue_number,
                    'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                    'numbers': numbers_list[i]
                })

        # 统计信息
        interval_stats = calculate_statistics(intervals) if intervals else {}

        result = {
            'target_pattern': target_pattern,
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),

            # 出现统计
            'occurrence_count': len(occurrences),
            'occurrence_frequency': len(occurrences) / len(patterns) if patterns else 0,

            # 间隔统计
            'intervals': intervals,
            'interval_statistics': interval_stats,
            'missing_periods': missing_periods,

            # 出现详情
            'occurrences': occurrences,

            # 最近出现
            'last_occurrence': occurrences[-1] if occurrences else None
        }

        return result
    
    def calculate_number_intervals(self, number: int, zone_type: str = 'front') -> Dict[str, Any]:
        """
        计算单个号码的遗漏间隔

        Args:
            number (int): 号码
            zone_type (str): 区域类型 ('front' 前区 或 'back' 后区)

        Returns:
            Dict[str, Any]: 号码间隔分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        # 验证号码范围
        if zone_type == 'front':
            if not (1 <= number <= 35):
                raise ValueError("前区号码必须在1-35之间")
            zone_name = "前区"
        elif zone_type == 'back':
            if not (1 <= number <= 12):
                raise ValueError("后区号码必须在1-12之间")
            zone_name = "后区"
        else:
            raise ValueError("zone_type 必须是 'front' 或 'back'")

        logger.info(f"开始大乐透{zone_name}号码 {number} 的遗漏分析")

        # 提取对应区域的号码
        if zone_type == 'front':
            numbers_list = [data.get_front_numbers() for data in self.data_list]
        else:
            numbers_list = [data.get_back_numbers() for data in self.data_list]

        # 查找号码出现的位置
        occurrences = []
        intervals = []
        last_position = -1

        for i, numbers in enumerate(numbers_list):
            if not numbers:
                continue

            if number in numbers:
                # 记录出现位置
                occurrences.append({
                    'position': i + 1,
                    'issue_number': self.data_list[i].issue_number,
                    'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                    'numbers': numbers
                })

                # 计算间隔
                if last_position != -1:
                    intervals.append(i - last_position)
                last_position = i

        # 计算当前遗漏期数
        current_missing = len(numbers_list) - 1 - last_position if last_position != -1 else len(numbers_list)

        # 统计信息
        interval_stats = calculate_statistics(intervals) if intervals else {}

        result = {
            'number': number,
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(numbers_list),

            # 出现统计
            'occurrence_count': len(occurrences),
            'occurrence_frequency': len(occurrences) / len(numbers_list) if numbers_list else 0,

            # 遗漏统计
            'current_missing_periods': current_missing,
            'intervals': intervals,
            'interval_statistics': interval_stats,

            # 出现详情
            'occurrences': occurrences,

            # 最近出现
            'last_occurrence': occurrences[-1] if occurrences else None
        }

        logger.info(f"大乐透{zone_name}号码 {number} 遗漏分析完成，出现 {len(occurrences)} 次，当前遗漏 {current_missing} 期")
        return result

    def calculate_all_numbers_missing(self, zone_type: str = 'front') -> Dict[str, Any]:
        """
        计算所有号码的遗漏情况统计

        Args:
            zone_type (str): 区域类型 ('front' 前区 或 'back' 后区)

        Returns:
            Dict[str, Any]: 所有号码遗漏统计结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        logger.info(f"开始大乐透{zone_type}区所有号码遗漏统计")

        # 确定号码范围
        if zone_type == 'front':
            number_range = range(1, 36)  # 1-35
            zone_name = "前区"
        elif zone_type == 'back':
            number_range = range(1, 13)  # 1-12
            zone_name = "后区"
        else:
            raise ValueError("zone_type 必须是 'front' 或 'back'")

        # 计算每个号码的遗漏情况
        missing_stats = []

        for number in number_range:
            result = self.calculate_number_intervals(number, zone_type)
            missing_stats.append({
                'number': number,
                'current_missing': result['current_missing_periods'],
                'occurrence_count': result['occurrence_count'],
                'occurrence_frequency': result['occurrence_frequency'],
                'avg_interval': result['interval_statistics'].get('mean', 0) if result['interval_statistics'] else 0,
                'max_interval': result['interval_statistics'].get('max', 0) if result['interval_statistics'] else 0,
                'last_occurrence': result['last_occurrence']
            })

        # 按遗漏期数排序
        missing_stats_sorted = sorted(missing_stats, key=lambda x: x['current_missing'], reverse=True)

        # 统计信息
        missing_periods = [stat['current_missing'] for stat in missing_stats]
        frequencies = [stat['occurrence_frequency'] for stat in missing_stats]

        result = {
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_numbers': len(number_range),
            'total_periods': len(self.data_list),
            'date_range': format_date_range(self.data_list[0].draw_date, self.data_list[-1].draw_date),

            # 遗漏统计
            'missing_statistics': calculate_statistics(missing_periods),
            'frequency_statistics': calculate_statistics(frequencies),

            # 排序结果
            'numbers_by_missing': missing_stats_sorted,
            'most_missing': missing_stats_sorted[:10],  # 遗漏最多的10个号码
            'least_missing': missing_stats_sorted[-10:],  # 遗漏最少的10个号码

            # 详细统计
            'all_numbers_stats': missing_stats
        }

        logger.info(f"大乐透{zone_name}所有号码遗漏统计完成，共统计 {len(number_range)} 个号码")
        return result
    
    def analyze_zone_ratio(self, zone_type: str = 'front') -> Dict[str, Any]:
        """
        分析分区比例

        Args:
            zone_type (str): 区域类型 ('front' 前区 或 'back' 后区)

        Returns:
            Dict[str, Any]: 分区比分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        logger.info(f"开始大乐透{zone_type}区分区比分析")

        # 提取对应区域的号码
        if zone_type == 'front':
            numbers_list = [data.get_front_numbers() for data in self.data_list]
            zone_name = "前区"
        elif zone_type == 'back':
            numbers_list = [data.get_back_numbers() for data in self.data_list]
            zone_name = "后区"
        else:
            raise ValueError("zone_type 必须是 'front' 或 'back'")

        # 生成分区比模式列表
        patterns = []
        detailed_results = []

        for i, numbers in enumerate(numbers_list):
            if not numbers:
                continue

            # 计算分区分布
            zone_distribution = count_zone_distribution(numbers, LOTTERY_TYPE_DLT, zone_type)
            pattern = generate_zone_ratio_pattern(numbers, LOTTERY_TYPE_DLT, zone_type)

            patterns.append(pattern)

            detailed_results.append({
                'issue_number': self.data_list[i].issue_number,
                'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                'numbers': numbers,
                'zone_distribution': zone_distribution,
                'pattern': pattern
            })

        # 计算模式统计
        pattern_stats = self.calculate_pattern_statistics(patterns)

        # 统计各分区的总体分布
        from data.config import get_zones
        zones = get_zones(LOTTERY_TYPE_DLT, zone_type)
        zone_totals = {zone['name']: 0 for zone in zones}

        for result in detailed_results:
            for zone_name_key, count in result['zone_distribution'].items():
                if zone_name_key in zone_totals:
                    zone_totals[zone_name_key] += count

        # 获取最新的分区比模式
        latest_pattern = patterns[-1] if patterns else ""
        latest_missing = self.get_missing_periods(patterns, latest_pattern)

        result = {
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),
            'date_range': format_date_range(self.data_list[0].draw_date, self.data_list[-1].draw_date),

            # 分区定义
            'zone_definitions': zones,

            # 模式统计
            'pattern_statistics': pattern_stats,
            'most_common_patterns': sorted(pattern_stats.items(),
                                         key=lambda x: x[1]['count'], reverse=True)[:10],

            # 分区总体统计
            'zone_totals': zone_totals,
            'zone_averages': {zone: total / len(patterns) for zone, total in zone_totals.items()} if patterns else {},

            # 最新状态
            'latest_pattern': latest_pattern,
            'latest_missing_periods': latest_missing,

            # 详细结果（最近20期）
            'recent_details': detailed_results[-20:],

            # 全部详细结果
            'all_details': detailed_results
        }

        logger.info(f"大乐透{zone_name}分区比分析完成，共分析 {len(patterns)} 期数据")
        return result

    def calculate_zone_ratio_intervals(self, target_pattern: str, zone_type: str = 'front') -> Dict[str, Any]:
        """
        计算指定分区比模式的出现间隔

        Args:
            target_pattern (str): 目标分区比模式（如 "2:1:2:0:0:0:0"）
            zone_type (str): 区域类型 ('front' 或 'back')

        Returns:
            Dict[str, Any]: 间隔分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        # 获取所有分区比模式
        if zone_type == 'front':
            numbers_list = [data.get_front_numbers() for data in self.data_list]
            zone_name = "前区"
        elif zone_type == 'back':
            numbers_list = [data.get_back_numbers() for data in self.data_list]
            zone_name = "后区"
        else:
            raise ValueError("zone_type 必须是 'front' 或 'back'")

        patterns = [generate_zone_ratio_pattern(numbers, LOTTERY_TYPE_DLT, zone_type) for numbers in numbers_list if numbers]

        # 计算间隔
        intervals = self.find_pattern_intervals(patterns, target_pattern)
        missing_periods = self.get_missing_periods(patterns, target_pattern)

        # 查找所有出现的位置和日期
        occurrences = []
        for i, pattern in enumerate(patterns):
            if pattern == target_pattern:
                zone_distribution = count_zone_distribution(numbers_list[i], LOTTERY_TYPE_DLT, zone_type)
                occurrences.append({
                    'position': i + 1,
                    'issue_number': self.data_list[i].issue_number,
                    'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                    'numbers': numbers_list[i],
                    'zone_distribution': zone_distribution
                })

        # 统计信息
        interval_stats = calculate_statistics(intervals) if intervals else {}

        result = {
            'target_pattern': target_pattern,
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),

            # 出现统计
            'occurrence_count': len(occurrences),
            'occurrence_frequency': len(occurrences) / len(patterns) if patterns else 0,

            # 间隔统计
            'intervals': intervals,
            'interval_statistics': interval_stats,
            'missing_periods': missing_periods,

            # 出现详情
            'occurrences': occurrences,

            # 最近出现
            'last_occurrence': occurrences[-1] if occurrences else None
        }

        return result
