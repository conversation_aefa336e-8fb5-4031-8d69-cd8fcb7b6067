"""
基础分析器类

定义彩票分析的基础类和通用接口，为具体的分析器提供框架和通用方法。
所有具体的分析器都应该继承自LotteryAnalyzer基类。
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import logging

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from data.models import LotteryData
from data.config import get_lottery_rule, LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5

# 配置日志
logger = logging.getLogger(__name__)


class LotteryAnalyzer(ABC):
    """
    彩票分析器基类
    
    提供彩票分析的通用框架和接口定义。所有具体的分析器都应该继承此类。
    """
    
    def __init__(self, lottery_type: str):
        """
        初始化分析器
        
        Args:
            lottery_type (str): 彩票类型 ('dlt', 'ssq', 'pl5')
        """
        self.lottery_type = lottery_type
        self.data_list: List[LotteryData] = []
        self.rules = get_lottery_rule(lottery_type)
        
        # 验证彩票类型
        if lottery_type not in [LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5]:
            raise ValueError(f"不支持的彩票类型: {lottery_type}")
        
        logger.info(f"初始化 {self.rules['display_name']} 分析器")
    
    def load_data(self, data_list: List[LotteryData]) -> None:
        """
        加载彩票数据

        Args:
            data_list (List[LotteryData]): 彩票数据列表
        """
        # 验证输入数据类型
        if not isinstance(data_list, list):
            raise ValueError("数据必须是列表类型")

        # 验证列表中的每个元素都是LotteryData对象
        from src.data.models import LotteryData
        for i, data in enumerate(data_list):
            if not isinstance(data, LotteryData):
                raise ValueError(f"列表中第{i+1}个元素不是LotteryData对象，而是{type(data)}")

        # 过滤出匹配的彩票类型数据
        filtered_data = [data for data in data_list if data.lottery_type == self.lottery_type]

        if not filtered_data:
            raise ValueError(f"没有找到 {self.lottery_type} 类型的数据")

        # 按日期排序（从旧到新）
        self.data_list = sorted(filtered_data, key=lambda x: x.draw_date)

        logger.info(f"加载了 {len(self.data_list)} 条 {self.rules['display_name']} 数据")
    
    def get_data_count(self) -> int:
        """
        获取数据数量
        
        Returns:
            int: 数据条数
        """
        return len(self.data_list)
    
    def get_date_range(self) -> Tuple[datetime, datetime]:
        """
        获取数据的日期范围
        
        Returns:
            Tuple[datetime, datetime]: (最早日期, 最新日期)
        """
        if not self.data_list:
            raise ValueError("没有数据")
        
        return self.data_list[0].draw_date, self.data_list[-1].draw_date
    
    def get_latest_data(self, count: int = 1) -> List[LotteryData]:
        """
        获取最新的几期数据
        
        Args:
            count (int): 获取的期数，默认为1
            
        Returns:
            List[LotteryData]: 最新的数据列表
        """
        if not self.data_list:
            return []
        
        return self.data_list[-count:] if count <= len(self.data_list) else self.data_list
    
    def find_pattern_intervals(self, pattern_list: List[str], target_pattern: str) -> List[int]:
        """
        查找指定模式的出现间隔
        
        Args:
            pattern_list (List[str]): 模式列表（按时间顺序）
            target_pattern (str): 目标模式
            
        Returns:
            List[int]: 间隔列表
        """
        intervals = []
        last_index = -1
        
        for i, pattern in enumerate(pattern_list):
            if pattern == target_pattern:
                if last_index != -1:
                    intervals.append(i - last_index)
                last_index = i
        
        return intervals
    
    def calculate_pattern_statistics(self, pattern_list: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        计算模式统计信息
        
        Args:
            pattern_list (List[str]): 模式列表
            
        Returns:
            Dict[str, Dict[str, Any]]: 模式统计信息
        """
        if not pattern_list:
            return {}
        
        # 过滤掉None值并统计每个模式的出现次数
        pattern_count = {}
        for pattern in pattern_list:
            if pattern is not None:  # 过滤掉None值
                pattern_count[pattern] = pattern_count.get(pattern, 0) + 1
        
        # 计算统计信息
        valid_count = sum(pattern_count.values())  # 有效模式的总数（排除None）
        statistics = {}

        for pattern, count in pattern_count.items():
            intervals = self.find_pattern_intervals(pattern_list, pattern)

            statistics[pattern] = {
                'count': count,
                'frequency': count / valid_count if valid_count > 0 else 0,
                'intervals': intervals,
                'avg_interval': sum(intervals) / len(intervals) if intervals else 0,
                'max_interval': max(intervals) if intervals else 0,
                'min_interval': min(intervals) if intervals else 0,
                'last_occurrence': len(pattern_list) - 1 - pattern_list[::-1].index(pattern)
            }
        
        return statistics
    
    def get_missing_periods(self, pattern_list: List[str], target_pattern: str) -> int:
        """
        获取指定模式的遗漏期数
        
        Args:
            pattern_list (List[str]): 模式列表（按时间顺序）
            target_pattern (str): 目标模式
            
        Returns:
            int: 遗漏期数（从最后一次出现到现在）
        """
        if not pattern_list or target_pattern not in pattern_list:
            return len(pattern_list)
        
        # 从后往前查找最后一次出现的位置
        last_index = len(pattern_list) - 1 - pattern_list[::-1].index(target_pattern)
        return len(pattern_list) - 1 - last_index
    
    # 抽象方法 - 子类必须实现
    
    @abstractmethod
    def analyze_odd_even_distribution(self, zone_type: str = 'front') -> Dict[str, Any]:
        """
        分析奇偶分布
        
        Args:
            zone_type (str): 区域类型 ('front' 或 'back')
            
        Returns:
            Dict[str, Any]: 奇偶分析结果
        """
        pass
    
    @abstractmethod
    def analyze_big_small_distribution(self, zone_type: str = 'front') -> Dict[str, Any]:
        """
        分析大小分布
        
        Args:
            zone_type (str): 区域类型 ('front' 或 'back')
            
        Returns:
            Dict[str, Any]: 大小分析结果
        """
        pass
    
    @abstractmethod
    def calculate_number_intervals(self, number: int, zone_type: str = 'front') -> Dict[str, Any]:
        """
        计算单个号码的遗漏间隔
        
        Args:
            number (int): 号码
            zone_type (str): 区域类型 ('front' 或 'back')
            
        Returns:
            Dict[str, Any]: 号码间隔分析结果
        """
        pass
    
    @abstractmethod
    def analyze_zone_ratio(self, zone_type: str = 'front') -> Dict[str, Any]:
        """
        分析分区比例
        
        Args:
            zone_type (str): 区域类型 ('front' 或 'back')
            
        Returns:
            Dict[str, Any]: 分区比分析结果
        """
        pass
    
    def __str__(self) -> str:
        """
        字符串表示
        
        Returns:
            str: 分析器描述
        """
        return f"{self.rules['display_name']}分析器 (数据: {len(self.data_list)} 条)"
    
    def __repr__(self) -> str:
        """
        对象表示
        
        Returns:
            str: 对象详细表示
        """
        return f"LotteryAnalyzer(lottery_type='{self.lottery_type}', data_count={len(self.data_list)})"
