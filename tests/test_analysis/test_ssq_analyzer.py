#!/usr/bin/env python3
"""
双色球分析器测试模块
测试 SSQAnalyzer 类的各种分析功能
"""

import pytest
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.analysis.ssq_analyzer import SSQAnalyzer
from src.data.models import LotteryData
from src.data.config import LOTTERY_TYPE_SSQ


class TestSSQAnalyzer:
    """双色球分析器基础功能测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = SSQAnalyzer()
        
        # 创建测试数据
        self.test_data = [
            LotteryData("24001", datetime(2024, 1, 2), [1, 5, 15, 25, 30, 33, 8], LOTTERY_TYPE_SSQ),
            LotteryData("24002", datetime(2024, 1, 4), [3, 7, 17, 27, 31, 32, 2], LOTTERY_TYPE_SSQ),
            LotteryData("24003", datetime(2024, 1, 7), [2, 8, 18, 28, 29, 33, 15], LOTTERY_TYPE_SSQ),
            LotteryData("24004", datetime(2024, 1, 9), [4, 9, 19, 26, 30, 32, 1], LOTTERY_TYPE_SSQ),
            LotteryData("24005", datetime(2024, 1, 11), [6, 11, 21, 24, 31, 33, 12], LOTTERY_TYPE_SSQ)
        ]
        
        self.analyzer.load_data(self.test_data)
    
    def test_analyze_odd_even_distribution_front(self):
        """测试红球区奇偶分析"""
        result = self.analyzer.analyze_odd_even_distribution('front')
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '红球区'
        assert result['total_periods'] == 5
        
        # 验证详细结果
        for detail in result['recent_details']:
            assert len(detail['pattern']) == 6  # 红球区6个号码
            assert detail['odd_count'] + detail['even_count'] == 6
    
    def test_analyze_odd_even_distribution_back(self):
        """测试蓝球区奇偶分析"""
        result = self.analyzer.analyze_odd_even_distribution('back')
        
        assert result['zone_type'] == 'back'
        assert result['zone_name'] == '蓝球区'
        assert result['total_periods'] == 5
        
        # 验证详细结果
        for detail in result['recent_details']:
            assert len(detail['pattern']) == 1  # 蓝球区1个号码
            assert detail['odd_count'] + detail['even_count'] == 1
    
    def test_analyze_big_small_distribution_front(self):
        """测试红球区大小分析"""
        result = self.analyzer.analyze_big_small_distribution('front')
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '红球区'
        assert result['total_periods'] == 5
        
        # 验证详细结果
        for detail in result['recent_details']:
            assert len(detail['pattern']) == 6  # 红球区6个号码
            assert detail['big_count'] + detail['small_count'] == 6
    
    def test_analyze_big_small_distribution_back(self):
        """测试蓝球区大小分析"""
        result = self.analyzer.analyze_big_small_distribution('back')
        
        assert result['zone_type'] == 'back'
        assert result['zone_name'] == '蓝球区'
        
        # 验证详细结果
        for detail in result['recent_details']:
            assert len(detail['pattern']) == 1  # 蓝球区1个号码
            assert detail['big_count'] + detail['small_count'] == 1
    
    def test_analyze_zone_ratio_front(self):
        """测试红球区分区比分析"""
        result = self.analyzer.analyze_zone_ratio('front')
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '红球区'
        assert result['total_periods'] == 5
        
        # 验证分区定义
        assert len(result['zone_definitions']) > 0
        
        # 验证详细结果
        for detail in result['recent_details']:
            assert 'zone_distribution' in detail
            assert isinstance(detail['zone_distribution'], dict)
    
    def test_analyze_zone_ratio_back(self):
        """测试蓝球区分区比分析"""
        result = self.analyzer.analyze_zone_ratio('back')
        
        assert result['zone_type'] == 'back'
        assert result['zone_name'] == '蓝球区'
    
    def test_calculate_number_intervals_front(self):
        """测试红球区号码遗漏分析"""
        result = self.analyzer.calculate_number_intervals(15, 'front')
        
        assert result['number'] == 15
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '红球区'
        assert result['total_periods'] == 5
    
    def test_calculate_number_intervals_back(self):
        """测试蓝球区号码遗漏分析"""
        result = self.analyzer.calculate_number_intervals(8, 'back')
        
        assert result['number'] == 8
        assert result['zone_type'] == 'back'
        assert result['zone_name'] == '蓝球区'
    
    def test_calculate_all_numbers_missing_front(self):
        """测试红球区所有号码遗漏统计"""
        result = self.analyzer.calculate_all_numbers_missing('front')
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '红球区'
        assert result['total_numbers'] == 33  # 红球区1-33
        assert result['total_periods'] == 5
        
        # 验证号码统计
        assert len(result['all_numbers_stats']) == 33
        for stat in result['all_numbers_stats']:
            assert 1 <= stat['number'] <= 33
    
    def test_calculate_all_numbers_missing_back(self):
        """测试蓝球区所有号码遗漏统计"""
        result = self.analyzer.calculate_all_numbers_missing('back')
        
        assert result['zone_type'] == 'back'
        assert result['zone_name'] == '蓝球区'
        assert result['total_numbers'] == 16  # 蓝球区1-16
        
        # 验证号码统计
        assert len(result['all_numbers_stats']) == 16
        for stat in result['all_numbers_stats']:
            assert 1 <= stat['number'] <= 16


class TestSSQAnalyzerEdgeCases:
    """双色球分析器边界条件测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = SSQAnalyzer()
    
    def test_invalid_number_range_front(self):
        """测试红球区无效号码范围"""
        test_data = [
            LotteryData("24001", datetime(2024, 1, 2), [1, 5, 15, 25, 30, 33, 8], LOTTERY_TYPE_SSQ)
        ]
        self.analyzer.load_data(test_data)
        
        with pytest.raises(ValueError, match="红球区号码必须在1-33之间"):
            self.analyzer.calculate_number_intervals(0, 'front')
        
        with pytest.raises(ValueError, match="红球区号码必须在1-33之间"):
            self.analyzer.calculate_number_intervals(34, 'front')
    
    def test_invalid_number_range_back(self):
        """测试蓝球区无效号码范围"""
        test_data = [
            LotteryData("24001", datetime(2024, 1, 2), [1, 5, 15, 25, 30, 33, 8], LOTTERY_TYPE_SSQ)
        ]
        self.analyzer.load_data(test_data)
        
        with pytest.raises(ValueError, match="蓝球区号码必须在1-16之间"):
            self.analyzer.calculate_number_intervals(0, 'back')
        
        with pytest.raises(ValueError, match="蓝球区号码必须在1-16之间"):
            self.analyzer.calculate_number_intervals(17, 'back')


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
