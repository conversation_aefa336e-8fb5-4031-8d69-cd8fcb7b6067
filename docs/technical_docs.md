# 彩票号码分析系统技术文档

## 目录
1. [系统架构](#系统架构)
2. [模块设计](#模块设计)
3. [数据模型](#数据模型)
4. [核心算法](#核心算法)
5. [API接口](#api接口)
6. [性能优化](#性能优化)
7. [测试策略](#测试策略)
8. [部署指南](#部署指南)

## 系统架构

### 整体架构
系统采用分层架构设计，包含以下层次：
```
┌─────────────────────────────────────┐
│           用户界面层 (UI)            │
│        tkinter + 自定义控件          │
├─────────────────────────────────────┤
│          业务逻辑层 (BLL)           │
│       应用控制器 + 分析器            │
├─────────────────────────────────────┤
│          数据访问层 (DAL)           │
│       数据读取器 + 验证器            │
├─────────────────────────────────────┤
│           数据模型层 (DML)          │
│        数据模型 + 配置定义           │
└─────────────────────────────────────┘
```

### 技术栈
- **编程语言**: Python 3.8+
- **GUI框架**: tkinter
- **数据处理**: pandas, numpy
- **测试框架**: pytest
- **代码质量**: 类型提示, 文档字符串

### 设计模式
- **MVC模式**: 分离界面、逻辑和数据
- **工厂模式**: 分析器创建
- **策略模式**: 不同彩票类型的分析策略
- **观察者模式**: UI事件处理

## 模块设计

### 目录结构
```
lottery_analysis/
├── src/                    # 源代码目录
│   ├── data/              # 数据处理模块
│   │   ├── __init__.py
│   │   ├── models.py      # 数据模型
│   │   ├── config.py      # 配置定义
│   │   ├── reader.py      # 数据读取器
│   │   └── validator.py   # 数据验证器
│   ├── analysis/          # 分析模块
│   │   ├── __init__.py
│   │   ├── base.py        # 基础分析器
│   │   ├── utils.py       # 工具函数
│   │   ├── dlt_analyzer.py    # 大乐透分析器
│   │   ├── ssq_analyzer.py    # 双色球分析器
│   │   └── pl5_analyzer.py    # 排列五分析器
│   └── ui/                # 用户界面模块
│       ├── __init__.py
│       ├── main_window.py # 主窗口
│       ├── widgets.py     # 自定义控件
│       ├── dialogs.py     # 对话框
│       └── app_controller.py  # 应用控制器
├── tests/                 # 测试目录
│   ├── test_data/         # 数据模块测试
│   ├── test_analysis/     # 分析模块测试
│   ├── test_ui/           # UI模块测试
│   └── test_integration/  # 集成测试
├── docs/                  # 文档目录
├── data/                  # 数据文件目录
├── requirements.txt       # 依赖文件
├── main.py               # 主程序入口
└── README.md             # 项目说明
```

### 核心模块

#### 1. 数据处理模块 (src/data/)
- **models.py**: 定义LotteryData数据模型
- **config.py**: 彩票规则和配置常量
- **reader.py**: CSV文件读取和解析
- **validator.py**: 数据验证和质量检查

#### 2. 分析模块 (src/analysis/)
- **base.py**: 抽象基类，定义通用分析接口
- **utils.py**: 分析工具函数
- **dlt_analyzer.py**: 大乐透专用分析器
- **ssq_analyzer.py**: 双色球专用分析器
- **pl5_analyzer.py**: 排列五专用分析器

#### 3. 用户界面模块 (src/ui/)
- **main_window.py**: 主窗口界面
- **widgets.py**: 自定义UI控件
- **dialogs.py**: 专用对话框
- **app_controller.py**: 应用程序控制器

## 数据模型

### LotteryData类
```python
@dataclass
class LotteryData:
    issue_number: str      # 期号
    draw_date: datetime    # 开奖日期
    numbers: List[int]     # 开奖号码
    lottery_type: str      # 彩票类型
    
    def get_front_numbers(self) -> List[int]
    def get_back_numbers(self) -> List[int]
    def validate(self) -> bool
```

### 配置模型
```python
LOTTERY_RULES = {
    'dlt': {
        'display_name': '大乐透',
        'front_count': 5,
        'back_count': 2,
        'front_range': (1, 35),
        'back_range': (1, 12),
        'big_small_boundary': {'front': 18, 'back': 6},
        'zones': {'front': [(1, 12), (13, 24), (25, 35)]}
    }
}
```

## 核心算法

### 1. 奇偶分析算法
```python
def analyze_odd_even_distribution(self, zone_type: str) -> Dict:
    """
    奇偶分析算法
    1. 提取指定区域号码
    2. 生成奇偶模式字符串
    3. 统计模式出现频率
    4. 计算模式间隔
    """
    patterns = []
    for data in self.data_list:
        numbers = self.get_zone_numbers(data, zone_type)
        pattern = generate_odd_even_pattern(numbers)
        patterns.append(pattern)
    
    return self.calculate_pattern_statistics(patterns)
```

### 2. 大小分析算法
```python
def analyze_big_small_distribution(self, zone_type: str) -> Dict:
    """
    大小分析算法
    1. 获取大小分界点
    2. 生成大小模式字符串
    3. 统计模式分布
    4. 计算统计指标
    """
    boundary = get_big_small_boundary(self.lottery_type, zone_type)
    patterns = []
    for data in self.data_list:
        numbers = self.get_zone_numbers(data, zone_type)
        pattern = generate_big_small_pattern(numbers, boundary)
        patterns.append(pattern)
    
    return self.calculate_pattern_statistics(patterns)
```

### 3. 遗漏分析算法
```python
def calculate_all_numbers_missing(self, zone_type: str) -> Dict:
    """
    遗漏分析算法
    1. 获取号码范围
    2. 遍历历史数据
    3. 计算每个号码的遗漏期数
    4. 统计遗漏分布
    """
    number_range = get_number_range(self.lottery_type, zone_type)
    missing_stats = {}
    
    for number in number_range:
        missing_periods = self.calculate_number_missing(number, zone_type)
        missing_stats[number] = missing_periods
    
    return self.generate_missing_report(missing_stats)
```

### 4. 分区比分析算法
```python
def analyze_zone_ratio(self, zone_type: str) -> Dict:
    """
    分区比分析算法
    1. 获取分区定义
    2. 统计各分区号码数量
    3. 计算分区比例
    4. 生成分区比模式
    """
    zones = get_zone_definition(self.lottery_type, zone_type)
    patterns = []
    
    for data in self.data_list:
        numbers = self.get_zone_numbers(data, zone_type)
        zone_counts = self.count_numbers_in_zones(numbers, zones)
        pattern = self.generate_zone_ratio_pattern(zone_counts)
        patterns.append(pattern)
    
    return self.calculate_pattern_statistics(patterns)
```

## API接口

### 分析器接口
```python
class LotteryAnalyzer(ABC):
    @abstractmethod
    def analyze_odd_even_distribution(self, zone_type: str) -> Dict
    
    @abstractmethod
    def analyze_big_small_distribution(self, zone_type: str) -> Dict
    
    @abstractmethod
    def calculate_all_numbers_missing(self, zone_type: str) -> Dict
    
    @abstractmethod
    def analyze_zone_ratio(self, zone_type: str) -> Dict
```

### 数据读取器接口
```python
class DataReader:
    def read_csv(self, file_path: str, lottery_type: str) -> List[LotteryData]
    def parse_dlt_data(self, df: pd.DataFrame) -> List[LotteryData]
    def parse_ssq_data(self, df: pd.DataFrame) -> List[LotteryData]
    def parse_pl5_data(self, df: pd.DataFrame) -> List[LotteryData]
```

### 数据验证器接口
```python
class DataValidator:
    def validate_lottery_data(self, data: LotteryData) -> Tuple[bool, List[str]]
    def validate_data_list(self, data_list: List[LotteryData]) -> Dict
    def get_validation_summary(self, validation_result: Dict) -> str
```

## 性能优化

### 1. 数据处理优化
- **批量处理**: 使用pandas的向量化操作
- **内存管理**: 及时释放不需要的数据
- **缓存机制**: 缓存计算结果避免重复计算

### 2. 算法优化
- **时间复杂度**: 大部分算法为O(n)线性复杂度
- **空间复杂度**: 使用生成器减少内存占用
- **并行处理**: 可扩展支持多线程分析

### 3. UI优化
- **异步处理**: 使用多线程避免界面冻结
- **进度显示**: 提供实时进度反馈
- **响应式设计**: 支持窗口大小调整

### 性能指标
- **数据读取**: 1000条数据 < 10秒
- **数据验证**: 1000条数据 < 10秒
- **分析处理**: 1000条数据 < 60秒
- **内存使用**: 峰值 < 100MB

## 测试策略

### 1. 单元测试
- **覆盖率**: 目标90%以上
- **测试框架**: pytest
- **测试数据**: 模拟数据和真实数据

### 2. 集成测试
- **端到端测试**: 完整工作流程测试
- **性能测试**: 大数据量处理测试
- **兼容性测试**: 不同操作系统测试

### 3. 测试分类
```
tests/
├── test_data/           # 数据模块测试 (40个测试用例)
├── test_analysis/       # 分析模块测试 (102个测试用例)
├── test_ui/            # UI模块测试 (11个测试用例)
└── test_integration/   # 集成测试 (7个测试用例)
```

### 4. 测试执行
```bash
# 运行所有测试
pytest

# 运行特定模块测试
pytest tests/test_analysis/

# 运行覆盖率测试
pytest --cov=src

# 运行性能测试
pytest tests/test_integration/test_system_integration.py::TestSystemIntegration::test_system_performance
```

## 部署指南

### 1. 环境准备
```bash
# 创建虚拟环境
python -m venv lottery_env

# 激活环境
source lottery_env/bin/activate  # Linux/macOS
lottery_env\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置文件
```python
# config.py
DEBUG = False
LOG_LEVEL = 'INFO'
DATA_PATH = './data'
TEMP_PATH = './temp'
```

### 3. 启动脚本
```bash
#!/bin/bash
# start.sh
cd /path/to/lottery_analysis
source lottery_env/bin/activate
python main.py
```

### 4. 系统服务 (可选)
```ini
# lottery-analysis.service
[Unit]
Description=Lottery Analysis System
After=network.target

[Service]
Type=simple
User=lottery
WorkingDirectory=/opt/lottery_analysis
ExecStart=/opt/lottery_analysis/lottery_env/bin/python main.py
Restart=always

[Install]
WantedBy=multi-user.target
```

### 5. 容器化部署 (可选)
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8080

CMD ["python", "main.py"]
```

## 扩展开发

### 1. 添加新彩票类型
1. 在`config.py`中添加新类型配置
2. 在`reader.py`中添加解析方法
3. 创建专用分析器类
4. 添加相应测试用例

### 2. 添加新分析功能
1. 在基类中定义抽象方法
2. 在各分析器中实现具体逻辑
3. 在UI中添加相应选项
4. 编写测试用例

### 3. 性能优化
1. 使用Cython编译关键算法
2. 实现多进程并行处理
3. 添加数据库支持
4. 实现分布式计算

## 故障排除

### 1. 常见错误
- **ImportError**: 检查依赖包安装
- **FileNotFoundError**: 检查文件路径
- **ValueError**: 检查数据格式
- **MemoryError**: 减少数据量或增加内存

### 2. 日志分析
```python
# 启用详细日志
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 3. 性能分析
```python
# 使用cProfile分析性能
python -m cProfile -o profile.stats main.py
```

### 4. 内存分析
```python
# 使用memory_profiler分析内存
pip install memory_profiler
python -m memory_profiler main.py
```

## 版本管理

### 版本号规则
- **主版本号**: 重大架构变更
- **次版本号**: 新功能添加
- **修订号**: 错误修复

### 发布流程
1. 代码审查
2. 测试验证
3. 文档更新
4. 版本标记
5. 发布部署

---

**文档版本**: v1.0.0
**最后更新**: 2024-01-28
**维护者**: 开发团队
