"""
排列五分析器

实现排列五彩票的专用分析功能，包括奇偶分析、大小分析、号码遗漏分析、分区比分析。
排列五是5位数字，每位数字范围0-9，支持整体分析和按位分析。
"""

from typing import Dict, List, Any, Tuple
import logging

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from analysis.base import LotteryAnalyzer
from analysis.utils import (
    generate_odd_even_pattern, count_odd_even,
    generate_big_small_pattern, count_big_small,
    generate_zone_ratio_pattern, count_zone_distribution,
    calculate_statistics, format_date_range
)
from data.config import LOTTERY_TYPE_PL5

logger = logging.getLogger(__name__)


class PL5Analyzer(LotteryAnalyzer):
    """
    排列五分析器类
    
    专门用于排列五彩票的数据分析，支持整体分析和按位分析。
    """
    
    def __init__(self):
        """初始化排列五分析器"""
        super().__init__(LOTTERY_TYPE_PL5)
    
    def analyze_odd_even_distribution(self, zone_type: str = 'front') -> Dict[str, Any]:
        """
        分析奇偶分布
        
        Args:
            zone_type (str): 区域类型 ('front' 整体分析，排列五不区分前后区)
            
        Returns:
            Dict[str, Any]: 奇偶分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")
        
        logger.info("开始排列五奇偶分析")
        
        # 排列五不区分前后区，统一使用front_numbers
        numbers_list = [data.get_front_numbers() for data in self.data_list]
        zone_name = "整体"
        
        # 生成奇偶模式列表
        patterns = []
        odd_counts = []
        even_counts = []
        detailed_results = []
        
        # 按位奇偶统计
        position_odd_counts = [[] for _ in range(5)]  # 5位数字
        position_even_counts = [[] for _ in range(5)]
        
        for i, numbers in enumerate(numbers_list):
            if not numbers or len(numbers) != 5:
                continue
                
            pattern = generate_odd_even_pattern(numbers)
            odd_count, even_count = count_odd_even(numbers)
            
            patterns.append(pattern)
            odd_counts.append(odd_count)
            even_counts.append(even_count)
            
            # 按位统计
            for pos in range(5):
                if numbers[pos] % 2 == 1:  # 奇数
                    position_odd_counts[pos].append(1)
                    position_even_counts[pos].append(0)
                else:  # 偶数
                    position_odd_counts[pos].append(0)
                    position_even_counts[pos].append(1)
            
            detailed_results.append({
                'issue_number': self.data_list[i].issue_number,
                'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                'numbers': numbers,
                'pattern': pattern,
                'odd_count': odd_count,
                'even_count': even_count,
                'position_patterns': [str(numbers[j] % 2) for j in range(5)]  # 按位奇偶
            })
        
        # 计算模式统计
        pattern_stats = self.calculate_pattern_statistics(patterns)
        
        # 计算奇偶数量统计
        odd_stats = calculate_statistics(odd_counts)
        even_stats = calculate_statistics(even_counts)
        
        # 按位奇偶统计
        position_stats = []
        for pos in range(5):
            odd_rate = sum(position_odd_counts[pos]) / len(position_odd_counts[pos]) if position_odd_counts[pos] else 0
            even_rate = sum(position_even_counts[pos]) / len(position_even_counts[pos]) if position_even_counts[pos] else 0
            position_stats.append({
                'position': pos + 1,
                'odd_rate': odd_rate,
                'even_rate': even_rate,
                'odd_count': sum(position_odd_counts[pos]),
                'even_count': sum(position_even_counts[pos])
            })
        
        # 获取最新的奇偶模式
        latest_pattern = patterns[-1] if patterns else ""
        latest_missing = self.get_missing_periods(patterns, latest_pattern)
        
        result = {
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),
            'date_range': format_date_range(self.data_list[0].draw_date, self.data_list[-1].draw_date),
            
            # 模式统计
            'pattern_statistics': pattern_stats,
            'most_common_patterns': sorted(pattern_stats.items(), 
                                         key=lambda x: x[1]['count'], reverse=True)[:10],
            
            # 奇偶数量统计
            'odd_count_stats': odd_stats,
            'even_count_stats': even_stats,
            
            # 按位奇偶统计
            'position_statistics': position_stats,
            
            # 最新状态
            'latest_pattern': latest_pattern,
            'latest_missing_periods': latest_missing,
            
            # 详细结果（最近20期）
            'recent_details': detailed_results[-20:],
            
            # 全部详细结果
            'all_details': detailed_results
        }
        
        logger.info(f"排列五奇偶分析完成，共分析 {len(patterns)} 期数据")
        return result
    
    def get_odd_even_pattern(self, issue_number: str = None, zone_type: str = 'front') -> str:
        """
        获取指定期号的奇偶模式代码
        
        Args:
            issue_number (str, optional): 期号，如果为None则返回最新期的模式
            zone_type (str): 区域类型（排列五不区分前后区）
            
        Returns:
            str: 奇偶模式代码
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")
        
        # 查找指定期号的数据
        target_data = None
        if issue_number is None:
            target_data = self.data_list[-1]  # 最新期
        else:
            for data in self.data_list:
                if data.issue_number == issue_number:
                    target_data = data
                    break
        
        if target_data is None:
            raise ValueError(f"未找到期号 {issue_number} 的数据")
        
        numbers = target_data.get_front_numbers()
        return generate_odd_even_pattern(numbers)
    
    def calculate_odd_even_intervals(self, target_pattern: str, zone_type: str = 'front') -> Dict[str, Any]:
        """
        计算指定奇偶模式的出现间隔
        
        Args:
            target_pattern (str): 目标奇偶模式（如 "10110"）
            zone_type (str): 区域类型（排列五不区分前后区）
            
        Returns:
            Dict[str, Any]: 间隔分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")
        
        # 获取所有奇偶模式
        numbers_list = [data.get_front_numbers() for data in self.data_list]
        zone_name = "整体"
        
        patterns = [generate_odd_even_pattern(numbers) for numbers in numbers_list if numbers and len(numbers) == 5]
        
        # 计算间隔
        intervals = self.find_pattern_intervals(patterns, target_pattern)
        missing_periods = self.get_missing_periods(patterns, target_pattern)
        
        # 查找所有出现的位置和日期
        occurrences = []
        for i, pattern in enumerate(patterns):
            if pattern == target_pattern:
                occurrences.append({
                    'position': i + 1,
                    'issue_number': self.data_list[i].issue_number,
                    'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                    'numbers': numbers_list[i]
                })
        
        # 统计信息
        interval_stats = calculate_statistics(intervals) if intervals else {}
        
        result = {
            'target_pattern': target_pattern,
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),
            
            # 出现统计
            'occurrence_count': len(occurrences),
            'occurrence_frequency': len(occurrences) / len(patterns) if patterns else 0,
            
            # 间隔统计
            'intervals': intervals,
            'interval_statistics': interval_stats,
            'missing_periods': missing_periods,
            
            # 出现详情
            'occurrences': occurrences,
            
            # 最近出现
            'last_occurrence': occurrences[-1] if occurrences else None
        }
        
        return result
    
    def analyze_position_odd_even(self, position: int) -> Dict[str, Any]:
        """
        分析指定位置的奇偶分布
        
        Args:
            position (int): 位置（1-5）
            
        Returns:
            Dict[str, Any]: 位置奇偶分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")
        
        if not (1 <= position <= 5):
            raise ValueError("位置必须在1-5之间")
        
        pos_index = position - 1  # 转换为0-4的索引
        
        # 提取指定位置的数字
        position_numbers = []
        position_patterns = []
        
        for data in self.data_list:
            numbers = data.get_front_numbers()
            if numbers and len(numbers) == 5:
                pos_number = numbers[pos_index]
                position_numbers.append(pos_number)
                position_patterns.append('1' if pos_number % 2 == 1 else '0')
        
        # 计算统计信息
        odd_count = sum(1 for num in position_numbers if num % 2 == 1)
        even_count = len(position_numbers) - odd_count
        
        pattern_stats = self.calculate_pattern_statistics(position_patterns)
        
        result = {
            'position': position,
            'total_periods': len(position_numbers),
            'odd_count': odd_count,
            'even_count': even_count,
            'odd_rate': odd_count / len(position_numbers) if position_numbers else 0,
            'even_rate': even_count / len(position_numbers) if position_numbers else 0,
            'pattern_statistics': pattern_stats,
            'recent_numbers': position_numbers[-20:],
            'recent_patterns': position_patterns[-20:]
        }
        
        return result
    
    def analyze_big_small_distribution(self, zone_type: str = 'front') -> Dict[str, Any]:
        """
        分析大小分布

        Args:
            zone_type (str): 区域类型 ('front' 整体分析，排列五不区分前后区)

        Returns:
            Dict[str, Any]: 大小分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        logger.info("开始排列五大小分析")

        # 排列五不区分前后区，统一使用front_numbers
        numbers_list = [data.get_front_numbers() for data in self.data_list]
        zone_name = "整体"

        # 生成大小模式列表
        patterns = []
        big_counts = []
        small_counts = []
        detailed_results = []

        # 按位大小统计
        position_big_counts = [[] for _ in range(5)]  # 5位数字
        position_small_counts = [[] for _ in range(5)]

        for i, numbers in enumerate(numbers_list):
            if not numbers or len(numbers) != 5:
                continue

            pattern = generate_big_small_pattern(numbers, LOTTERY_TYPE_PL5, zone_type)
            big_count, small_count = count_big_small(numbers, LOTTERY_TYPE_PL5, zone_type)

            patterns.append(pattern)
            big_counts.append(big_count)
            small_counts.append(small_count)

            # 按位统计
            for pos in range(5):
                if numbers[pos] >= 5:  # 大号（5-9）
                    position_big_counts[pos].append(1)
                    position_small_counts[pos].append(0)
                else:  # 小号（0-4）
                    position_big_counts[pos].append(0)
                    position_small_counts[pos].append(1)

            detailed_results.append({
                'issue_number': self.data_list[i].issue_number,
                'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                'numbers': numbers,
                'pattern': pattern,
                'big_count': big_count,
                'small_count': small_count,
                'position_patterns': [str(1 if numbers[j] >= 5 else 0) for j in range(5)]  # 按位大小
            })

        # 计算模式统计
        pattern_stats = self.calculate_pattern_statistics(patterns)

        # 计算大小数量统计
        big_stats = calculate_statistics(big_counts)
        small_stats = calculate_statistics(small_counts)

        # 按位大小统计
        position_stats = []
        for pos in range(5):
            big_rate = sum(position_big_counts[pos]) / len(position_big_counts[pos]) if position_big_counts[pos] else 0
            small_rate = sum(position_small_counts[pos]) / len(position_small_counts[pos]) if position_small_counts[pos] else 0
            position_stats.append({
                'position': pos + 1,
                'big_rate': big_rate,
                'small_rate': small_rate,
                'big_count': sum(position_big_counts[pos]),
                'small_count': sum(position_small_counts[pos])
            })

        # 获取最新的大小模式
        latest_pattern = patterns[-1] if patterns else ""
        latest_missing = self.get_missing_periods(patterns, latest_pattern)

        result = {
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),
            'date_range': format_date_range(self.data_list[0].draw_date, self.data_list[-1].draw_date),

            # 模式统计
            'pattern_statistics': pattern_stats,
            'most_common_patterns': sorted(pattern_stats.items(),
                                         key=lambda x: x[1]['count'], reverse=True)[:10],

            # 大小数量统计
            'big_count_stats': big_stats,
            'small_count_stats': small_stats,

            # 按位大小统计
            'position_statistics': position_stats,

            # 最新状态
            'latest_pattern': latest_pattern,
            'latest_missing_periods': latest_missing,

            # 详细结果（最近20期）
            'recent_details': detailed_results[-20:],

            # 全部详细结果
            'all_details': detailed_results
        }

        logger.info(f"排列五大小分析完成，共分析 {len(patterns)} 期数据")
        return result

    def get_big_small_pattern(self, issue_number: str = None, zone_type: str = 'front') -> str:
        """
        获取指定期号的大小模式代码

        Args:
            issue_number (str, optional): 期号，如果为None则返回最新期的模式
            zone_type (str): 区域类型（排列五不区分前后区）

        Returns:
            str: 大小模式代码
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        # 查找指定期号的数据
        target_data = None
        if issue_number is None:
            target_data = self.data_list[-1]  # 最新期
        else:
            for data in self.data_list:
                if data.issue_number == issue_number:
                    target_data = data
                    break

        if target_data is None:
            raise ValueError(f"未找到期号 {issue_number} 的数据")

        numbers = target_data.get_front_numbers()
        return generate_big_small_pattern(numbers, LOTTERY_TYPE_PL5, zone_type)

    def calculate_big_small_intervals(self, target_pattern: str, zone_type: str = 'front') -> Dict[str, Any]:
        """
        计算指定大小模式的出现间隔

        Args:
            target_pattern (str): 目标大小模式（如 "10110"）
            zone_type (str): 区域类型（排列五不区分前后区）

        Returns:
            Dict[str, Any]: 间隔分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        # 获取所有大小模式
        numbers_list = [data.get_front_numbers() for data in self.data_list]
        zone_name = "整体"

        patterns = [generate_big_small_pattern(numbers, LOTTERY_TYPE_PL5, zone_type) for numbers in numbers_list if numbers and len(numbers) == 5]

        # 计算间隔
        intervals = self.find_pattern_intervals(patterns, target_pattern)
        missing_periods = self.get_missing_periods(patterns, target_pattern)

        # 查找所有出现的位置和日期
        occurrences = []
        for i, pattern in enumerate(patterns):
            if pattern == target_pattern:
                occurrences.append({
                    'position': i + 1,
                    'issue_number': self.data_list[i].issue_number,
                    'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                    'numbers': numbers_list[i]
                })

        # 统计信息
        interval_stats = calculate_statistics(intervals) if intervals else {}

        result = {
            'target_pattern': target_pattern,
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),

            # 出现统计
            'occurrence_count': len(occurrences),
            'occurrence_frequency': len(occurrences) / len(patterns) if patterns else 0,

            # 间隔统计
            'intervals': intervals,
            'interval_statistics': interval_stats,
            'missing_periods': missing_periods,

            # 出现详情
            'occurrences': occurrences,

            # 最近出现
            'last_occurrence': occurrences[-1] if occurrences else None
        }

        return result

    def analyze_position_big_small(self, position: int) -> Dict[str, Any]:
        """
        分析指定位置的大小分布

        Args:
            position (int): 位置（1-5）

        Returns:
            Dict[str, Any]: 位置大小分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        if not (1 <= position <= 5):
            raise ValueError("位置必须在1-5之间")

        pos_index = position - 1  # 转换为0-4的索引

        # 提取指定位置的数字
        position_numbers = []
        position_patterns = []

        for data in self.data_list:
            numbers = data.get_front_numbers()
            if numbers and len(numbers) == 5:
                pos_number = numbers[pos_index]
                position_numbers.append(pos_number)
                position_patterns.append('1' if pos_number >= 5 else '0')

        # 计算统计信息
        big_count = sum(1 for num in position_numbers if num >= 5)
        small_count = len(position_numbers) - big_count

        pattern_stats = self.calculate_pattern_statistics(position_patterns)

        result = {
            'position': position,
            'total_periods': len(position_numbers),
            'big_count': big_count,
            'small_count': small_count,
            'big_rate': big_count / len(position_numbers) if position_numbers else 0,
            'small_rate': small_count / len(position_numbers) if position_numbers else 0,
            'pattern_statistics': pattern_stats,
            'recent_numbers': position_numbers[-20:],
            'recent_patterns': position_patterns[-20:]
        }

        return result
    
    def calculate_number_intervals(self, number: int, zone_type: str = 'front') -> Dict[str, Any]:
        """
        计算单个号码的遗漏间隔

        Args:
            number (int): 号码 (0-9)
            zone_type (str): 区域类型（排列五不区分前后区）

        Returns:
            Dict[str, Any]: 号码间隔分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        # 验证号码范围
        if not (0 <= number <= 9):
            raise ValueError("排列五号码必须在0-9之间")

        logger.info(f"开始排列五号码 {number} 的遗漏分析")

        # 排列五不区分前后区，统一使用front_numbers
        numbers_list = [data.get_front_numbers() for data in self.data_list]
        zone_name = "整体"

        # 查找号码出现的位置
        occurrences = []
        intervals = []
        last_position = -1

        for i, numbers in enumerate(numbers_list):
            if not numbers or len(numbers) != 5:
                continue

            if number in numbers:
                # 记录出现位置
                occurrences.append({
                    'position': i + 1,
                    'issue_number': self.data_list[i].issue_number,
                    'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                    'numbers': numbers,
                    'positions_in_number': [pos + 1 for pos, num in enumerate(numbers) if num == number]  # 出现在哪些位置
                })

                # 计算间隔
                if last_position != -1:
                    intervals.append(i - last_position)
                last_position = i

        # 计算当前遗漏期数
        current_missing = len(numbers_list) - 1 - last_position if last_position != -1 else len(numbers_list)

        # 统计信息
        interval_stats = calculate_statistics(intervals) if intervals else {}

        result = {
            'number': number,
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(numbers_list),

            # 出现统计
            'occurrence_count': len(occurrences),
            'occurrence_frequency': len(occurrences) / len(numbers_list) if numbers_list else 0,

            # 遗漏统计
            'current_missing_periods': current_missing,
            'intervals': intervals,
            'interval_statistics': interval_stats,

            # 出现详情
            'occurrences': occurrences,

            # 最近出现
            'last_occurrence': occurrences[-1] if occurrences else None
        }

        logger.info(f"排列五号码 {number} 遗漏分析完成，出现 {len(occurrences)} 次，当前遗漏 {current_missing} 期")
        return result

    def calculate_all_numbers_missing(self, zone_type: str = 'front') -> Dict[str, Any]:
        """
        计算所有号码的遗漏情况统计

        Args:
            zone_type (str): 区域类型（排列五不区分前后区）

        Returns:
            Dict[str, Any]: 所有号码遗漏统计结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        logger.info("开始排列五所有号码遗漏统计")

        # 排列五号码范围0-9
        number_range = range(0, 10)
        zone_name = "整体"

        # 计算每个号码的遗漏情况
        missing_stats = []

        for number in number_range:
            result = self.calculate_number_intervals(number, zone_type)
            missing_stats.append({
                'number': number,
                'current_missing': result['current_missing_periods'],
                'occurrence_count': result['occurrence_count'],
                'occurrence_frequency': result['occurrence_frequency'],
                'avg_interval': result['interval_statistics'].get('mean', 0) if result['interval_statistics'] else 0,
                'max_interval': result['interval_statistics'].get('max', 0) if result['interval_statistics'] else 0,
                'last_occurrence': result['last_occurrence']
            })

        # 按遗漏期数排序
        missing_stats_sorted = sorted(missing_stats, key=lambda x: x['current_missing'], reverse=True)

        # 统计信息
        missing_periods = [stat['current_missing'] for stat in missing_stats]
        frequencies = [stat['occurrence_frequency'] for stat in missing_stats]

        result = {
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_numbers': len(number_range),
            'total_periods': len(self.data_list),
            'date_range': format_date_range(self.data_list[0].draw_date, self.data_list[-1].draw_date),

            # 遗漏统计
            'missing_statistics': calculate_statistics(missing_periods),
            'frequency_statistics': calculate_statistics(frequencies),

            # 排序结果
            'numbers_by_missing': missing_stats_sorted,
            'most_missing': missing_stats_sorted[:5],  # 遗漏最多的5个号码
            'least_missing': missing_stats_sorted[-5:],  # 遗漏最少的5个号码

            # 详细统计
            'all_numbers_stats': missing_stats
        }

        logger.info(f"排列五所有号码遗漏统计完成，共统计 {len(number_range)} 个号码")
        return result

    def calculate_position_number_intervals(self, number: int, position: int) -> Dict[str, Any]:
        """
        计算指定位置上指定号码的遗漏间隔

        Args:
            number (int): 号码 (0-9)
            position (int): 位置 (1-5)

        Returns:
            Dict[str, Any]: 位置号码间隔分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        # 验证参数
        if not (0 <= number <= 9):
            raise ValueError("号码必须在0-9之间")
        if not (1 <= position <= 5):
            raise ValueError("位置必须在1-5之间")

        pos_index = position - 1  # 转换为0-4的索引

        logger.info(f"开始排列五第{position}位号码 {number} 的遗漏分析")

        # 提取指定位置的数字
        numbers_list = [data.get_front_numbers() for data in self.data_list]

        # 查找号码在指定位置出现的情况
        occurrences = []
        intervals = []
        last_position = -1

        for i, numbers in enumerate(numbers_list):
            if not numbers or len(numbers) != 5:
                continue

            if numbers[pos_index] == number:
                # 记录出现位置
                occurrences.append({
                    'position': i + 1,
                    'issue_number': self.data_list[i].issue_number,
                    'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                    'numbers': numbers,
                    'target_position': position
                })

                # 计算间隔
                if last_position != -1:
                    intervals.append(i - last_position)
                last_position = i

        # 计算当前遗漏期数
        current_missing = len(numbers_list) - 1 - last_position if last_position != -1 else len(numbers_list)

        # 统计信息
        interval_stats = calculate_statistics(intervals) if intervals else {}

        result = {
            'number': number,
            'position': position,
            'total_periods': len(numbers_list),

            # 出现统计
            'occurrence_count': len(occurrences),
            'occurrence_frequency': len(occurrences) / len(numbers_list) if numbers_list else 0,

            # 遗漏统计
            'current_missing_periods': current_missing,
            'intervals': intervals,
            'interval_statistics': interval_stats,

            # 出现详情
            'occurrences': occurrences,

            # 最近出现
            'last_occurrence': occurrences[-1] if occurrences else None
        }

        logger.info(f"排列五第{position}位号码 {number} 遗漏分析完成，出现 {len(occurrences)} 次，当前遗漏 {current_missing} 期")
        return result
    
    def analyze_zone_ratio(self, zone_type: str = 'front') -> Dict[str, Any]:
        """
        分析分区比例

        Args:
            zone_type (str): 区域类型（排列五不区分前后区）

        Returns:
            Dict[str, Any]: 分区比分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        logger.info("开始排列五分区比分析")

        # 排列五不区分前后区，统一使用front_numbers
        numbers_list = [data.get_front_numbers() for data in self.data_list]
        zone_name = "整体"

        # 生成分区比模式列表
        patterns = []
        detailed_results = []

        # 按位分区统计
        position_zone_stats = [{'zone1': 0, 'zone2': 0} for _ in range(5)]  # 5位数字

        for i, numbers in enumerate(numbers_list):
            if not numbers or len(numbers) != 5:
                continue

            # 计算分区分布
            zone_distribution = count_zone_distribution(numbers, LOTTERY_TYPE_PL5, zone_type)
            pattern = generate_zone_ratio_pattern(numbers, LOTTERY_TYPE_PL5, zone_type)

            patterns.append(pattern)

            # 按位分区统计
            for pos in range(5):
                if numbers[pos] >= 5:  # zone2 (5-9)
                    position_zone_stats[pos]['zone2'] += 1
                else:  # zone1 (0-4)
                    position_zone_stats[pos]['zone1'] += 1

            detailed_results.append({
                'issue_number': self.data_list[i].issue_number,
                'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                'numbers': numbers,
                'zone_distribution': zone_distribution,
                'pattern': pattern,
                'position_zones': [1 if numbers[j] < 5 else 2 for j in range(5)]  # 按位分区
            })

        # 计算模式统计
        pattern_stats = self.calculate_pattern_statistics(patterns)

        # 统计各分区的总体分布
        from data.config import get_zones
        zones = get_zones(LOTTERY_TYPE_PL5, zone_type)
        zone_totals = {zone['name']: 0 for zone in zones}

        for result in detailed_results:
            for zone_name_key, count in result['zone_distribution'].items():
                if zone_name_key in zone_totals:
                    zone_totals[zone_name_key] += count

        # 按位分区统计
        position_stats = []
        for pos in range(5):
            total = position_zone_stats[pos]['zone1'] + position_zone_stats[pos]['zone2']
            position_stats.append({
                'position': pos + 1,
                'zone1_count': position_zone_stats[pos]['zone1'],
                'zone2_count': position_zone_stats[pos]['zone2'],
                'zone1_rate': position_zone_stats[pos]['zone1'] / total if total > 0 else 0,
                'zone2_rate': position_zone_stats[pos]['zone2'] / total if total > 0 else 0
            })

        # 获取最新的分区比模式
        latest_pattern = patterns[-1] if patterns else ""
        latest_missing = self.get_missing_periods(patterns, latest_pattern)

        result = {
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),
            'date_range': format_date_range(self.data_list[0].draw_date, self.data_list[-1].draw_date),

            # 分区定义
            'zone_definitions': zones,

            # 模式统计
            'pattern_statistics': pattern_stats,
            'most_common_patterns': sorted(pattern_stats.items(),
                                         key=lambda x: x[1]['count'], reverse=True)[:10],

            # 分区总体统计
            'zone_totals': zone_totals,
            'zone_averages': {zone: total / len(patterns) for zone, total in zone_totals.items()} if patterns else {},

            # 按位分区统计
            'position_zone_statistics': position_stats,

            # 最新状态
            'latest_pattern': latest_pattern,
            'latest_missing_periods': latest_missing,

            # 详细结果（最近20期）
            'recent_details': detailed_results[-20:],

            # 全部详细结果
            'all_details': detailed_results
        }

        logger.info(f"排列五分区比分析完成，共分析 {len(patterns)} 期数据")
        return result

    def calculate_zone_ratio_intervals(self, target_pattern: str, zone_type: str = 'front') -> Dict[str, Any]:
        """
        计算指定分区比模式的出现间隔

        Args:
            target_pattern (str): 目标分区比模式（如 "3:2"）
            zone_type (str): 区域类型（排列五不区分前后区）

        Returns:
            Dict[str, Any]: 间隔分析结果
        """
        if not self.data_list:
            raise ValueError("没有数据，请先加载数据")

        # 获取所有分区比模式
        numbers_list = [data.get_front_numbers() for data in self.data_list]
        zone_name = "整体"

        patterns = [generate_zone_ratio_pattern(numbers, LOTTERY_TYPE_PL5, zone_type) for numbers in numbers_list if numbers and len(numbers) == 5]

        # 计算间隔
        intervals = self.find_pattern_intervals(patterns, target_pattern)
        missing_periods = self.get_missing_periods(patterns, target_pattern)

        # 查找所有出现的位置和日期
        occurrences = []
        for i, pattern in enumerate(patterns):
            if pattern == target_pattern:
                zone_distribution = count_zone_distribution(numbers_list[i], LOTTERY_TYPE_PL5, zone_type)
                occurrences.append({
                    'position': i + 1,
                    'issue_number': self.data_list[i].issue_number,
                    'draw_date': self.data_list[i].draw_date.strftime('%Y-%m-%d'),
                    'numbers': numbers_list[i],
                    'zone_distribution': zone_distribution
                })

        # 统计信息
        interval_stats = calculate_statistics(intervals) if intervals else {}

        result = {
            'target_pattern': target_pattern,
            'zone_type': zone_type,
            'zone_name': zone_name,
            'total_periods': len(patterns),

            # 出现统计
            'occurrence_count': len(occurrences),
            'occurrence_frequency': len(occurrences) / len(patterns) if patterns else 0,

            # 间隔统计
            'intervals': intervals,
            'interval_statistics': interval_stats,
            'missing_periods': missing_periods,

            # 出现详情
            'occurrences': occurrences,

            # 最近出现
            'last_occurrence': occurrences[-1] if occurrences else None
        }

        return result
