#!/usr/bin/env python3
"""
主窗口界面测试
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

# 模拟tkinter模块（用于无GUI环境测试）
try:
    import tkinter as tk
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False


class TestMainWindow(unittest.TestCase):
    """主窗口测试类"""
    
    def setUp(self):
        """测试前准备"""
        if not GUI_AVAILABLE:
            self.skipTest("GUI环境不可用")
        
        from src.ui.main_window import MainWindow
        self.main_window = MainWindow()
    
    def tearDown(self):
        """测试后清理"""
        if hasattr(self, 'main_window'):
            try:
                self.main_window.root.destroy()
            except:
                pass
    
    def test_window_initialization(self):
        """测试窗口初始化"""
        self.assertIsNotNone(self.main_window.root)
        self.assertEqual(self.main_window.root.title(), "彩票号码分析系统")
    
    def test_lottery_type_selection(self):
        """测试彩票类型选择"""
        from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5
        
        # 测试默认选择
        self.assertEqual(self.main_window.get_lottery_type(), LOTTERY_TYPE_DLT)
        
        # 测试切换彩票类型
        self.main_window.lottery_type_var.set(LOTTERY_TYPE_SSQ)
        self.assertEqual(self.main_window.get_lottery_type(), LOTTERY_TYPE_SSQ)
        
        self.main_window.lottery_type_var.set(LOTTERY_TYPE_PL5)
        self.assertEqual(self.main_window.get_lottery_type(), LOTTERY_TYPE_PL5)
    
    def test_analysis_options(self):
        """测试分析选项"""
        options = self.main_window.get_analysis_options()
        
        # 测试默认全选
        self.assertTrue(options['odd_even'])
        self.assertTrue(options['big_small'])
        self.assertTrue(options['missing'])
        self.assertTrue(options['zone_ratio'])
        
        # 测试取消选择
        self.main_window.analysis_options['odd_even'].set(False)
        options = self.main_window.get_analysis_options()
        self.assertFalse(options['odd_even'])
    
    def test_data_file_path(self):
        """测试数据文件路径"""
        test_path = "/test/path/data.csv"
        self.main_window.data_file_path.set(test_path)
        self.assertEqual(self.main_window.get_data_file_path(), test_path)
    
    def test_results_text_operations(self):
        """测试结果文本操作"""
        test_text = "测试结果文本"
        
        # 测试设置文本
        self.main_window.set_results_text(test_text)
        content = self.main_window.results_text.get(1.0, "end-1c")
        self.assertEqual(content, test_text)
        
        # 测试追加文本
        append_text = "\n追加文本"
        self.main_window.append_results_text(append_text)
        content = self.main_window.results_text.get(1.0, "end-1c")
        self.assertEqual(content, test_text + append_text)
        
        # 测试清除文本
        self.main_window.clear_results()
        content = self.main_window.results_text.get(1.0, "end-1c")
        self.assertEqual(content, "")
    
    def test_callback_functions(self):
        """测试回调函数"""
        # 创建模拟回调函数
        mock_import = Mock()
        mock_analysis = Mock()
        mock_export = Mock()
        mock_clear = Mock()
        
        # 设置回调函数
        self.main_window.on_import_data = mock_import
        self.main_window.on_start_analysis = mock_analysis
        self.main_window.on_export_results = mock_export
        self.main_window.on_clear_results = mock_clear
        
        # 测试调用
        self.main_window.import_data()
        mock_import.assert_called_once()
        
        self.main_window.start_analysis()
        mock_analysis.assert_called_once()
        
        self.main_window.export_results()
        mock_export.assert_called_once()
        
        self.main_window.clear_results()
        mock_clear.assert_called_once()


class TestMainWindowIntegration(unittest.TestCase):
    """主窗口集成测试"""
    
    def setUp(self):
        """测试前准备"""
        if not GUI_AVAILABLE:
            self.skipTest("GUI环境不可用")
    
    @patch('tkinter.filedialog.askopenfilename')
    def test_browse_file(self, mock_dialog):
        """测试文件浏览功能"""
        from src.ui.main_window import MainWindow
        
        # 模拟文件选择
        test_file = "/test/data.csv"
        mock_dialog.return_value = test_file
        
        main_window = MainWindow()
        main_window.browse_file()
        
        # 验证文件路径设置
        self.assertEqual(main_window.get_data_file_path(), test_file)
        
        main_window.root.destroy()
    
    @patch('tkinter.messagebox.showinfo')
    def test_show_help(self, mock_messagebox):
        """测试帮助功能"""
        from src.ui.main_window import MainWindow
        
        main_window = MainWindow()
        main_window.show_help()
        
        # 验证消息框被调用
        mock_messagebox.assert_called_once()
        args, kwargs = mock_messagebox.call_args
        self.assertEqual(args[0], "使用说明")
        
        main_window.root.destroy()
    
    @patch('tkinter.messagebox.showinfo')
    def test_show_about(self, mock_messagebox):
        """测试关于功能"""
        from src.ui.main_window import MainWindow
        
        main_window = MainWindow()
        main_window.show_about()
        
        # 验证消息框被调用
        mock_messagebox.assert_called_once()
        args, kwargs = mock_messagebox.call_args
        self.assertEqual(args[0], "关于")
        
        main_window.root.destroy()


class TestWidgets(unittest.TestCase):
    """自定义控件测试"""
    
    def setUp(self):
        """测试前准备"""
        if not GUI_AVAILABLE:
            self.skipTest("GUI环境不可用")
    
    def test_progress_dialog(self):
        """测试进度对话框"""
        from src.ui.widgets import ProgressDialog
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口
        
        progress = ProgressDialog(root, "测试", "测试消息")
        
        # 测试更新消息
        progress.update_message("新消息")
        self.assertEqual(progress.message_label.cget("text"), "新消息")
        
        # 关闭对话框
        progress.close()
        root.destroy()
    
    def test_status_bar(self):
        """测试状态栏"""
        from src.ui.widgets import StatusBar
        import tkinter as tk
        
        root = tk.Tk()
        root.withdraw()
        
        status_bar = StatusBar(root)
        
        # 测试设置状态
        status_bar.set_status("测试状态")
        self.assertEqual(status_bar.status_label.cget("text"), "测试状态")
        
        # 测试设置进度
        status_bar.set_progress("50%")
        self.assertEqual(status_bar.progress_label.cget("text"), "50%")
        
        # 测试清除
        status_bar.clear()
        self.assertEqual(status_bar.status_label.cget("text"), "就绪")
        self.assertEqual(status_bar.progress_label.cget("text"), "")
        
        root.destroy()


if __name__ == "__main__":
    # 运行测试
    unittest.main(verbosity=2)
