#!/usr/bin/env python3
"""
数据导入和分析功能集成测试
"""

import unittest
import sys
import os
import tempfile
import csv
from datetime import datetime
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.data.reader import DataReader
from src.data.validator import DataValidator
from src.data.models import LotteryData
from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5
from src.analysis.dlt_analyzer import DLTAnalyzer


class TestDataAnalysisIntegration(unittest.TestCase):
    """数据导入和分析集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.data_reader = DataReader()
        self.data_validator = DataValidator()
        self.temp_files = []
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        for temp_file in self.temp_files:
            try:
                os.unlink(temp_file)
            except:
                pass
    
    def create_temp_csv_file(self, data_rows: list, lottery_type: str) -> str:
        """创建临时CSV文件"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
        self.temp_files.append(temp_file.name)
        
        writer = csv.writer(temp_file)
        for row in data_rows:
            writer.writerow(row)
        
        temp_file.close()
        return temp_file.name
    
    def test_dlt_data_import_and_analysis(self):
        """测试大乐透数据导入和分析"""
        # 创建测试数据（包含列名）
        test_data = [
            ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"],
            ["24001", "2024-01-01", "1", "5", "15", "25", "35", "2", "8"],
            ["24002", "2024-01-03", "3", "7", "17", "27", "33", "1", "9"],
            ["24003", "2024-01-06", "2", "8", "18", "28", "34", "3", "10"],
            ["24004", "2024-01-08", "4", "9", "19", "29", "31", "5", "11"],
            ["24005", "2024-01-10", "6", "11", "21", "30", "32", "4", "12"]
        ]
        
        # 创建临时文件
        temp_file = self.create_temp_csv_file(test_data, LOTTERY_TYPE_DLT)
        
        # 读取数据
        data_list = self.data_reader.read_csv(temp_file, LOTTERY_TYPE_DLT)
        
        # 验证数据
        validation_result = self.data_validator.validate_data_list(data_list)
        
        # 检查验证结果
        self.assertEqual(validation_result['total_count'], 5)
        self.assertEqual(validation_result['valid_count'], 5)
        self.assertEqual(validation_result['invalid_count'], 0)
        
        # 创建分析器并分析
        analyzer = DLTAnalyzer()
        analyzer.load_data(data_list)
        
        # 测试奇偶分析
        odd_even_result = analyzer.analyze_odd_even_distribution('front')
        self.assertIn('total_periods', odd_even_result)
        self.assertIn('latest_pattern', odd_even_result)
        self.assertEqual(odd_even_result['total_periods'], 5)
        
        # 测试大小分析
        big_small_result = analyzer.analyze_big_small_distribution('front')
        self.assertIn('total_periods', big_small_result)
        self.assertIn('latest_pattern', big_small_result)
        
        # 测试遗漏分析
        missing_result = analyzer.calculate_all_numbers_missing('front')
        self.assertIn('total_numbers', missing_result)
        self.assertIn('total_periods', missing_result)
        
        # 测试分区比分析
        zone_result = analyzer.analyze_zone_ratio('front')
        self.assertIn('total_periods', zone_result)
        self.assertIn('latest_pattern', zone_result)
    
    def test_invalid_data_handling(self):
        """测试无效数据处理"""
        # 创建包含无效数据的测试数据
        test_data = [
            ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"],
            ["24001", "2024-01-01", "1", "5", "15", "25", "35", "2", "8"],  # 有效
            ["invalid", "2024-01-03", "1", "2", "3", "", "", "", ""],  # 无效：号码数量不足
            ["24003", "2024-01-06", "40", "50", "60", "70", "80", "20", "30"],  # 无效：号码超出范围
            ["24004", "2024-01-08", "1", "1", "2", "2", "3", "1", "2"],  # 无效：前区重复
            ["24005", "2024-01-10", "6", "11", "21", "30", "32", "4", "12"]  # 有效
        ]
        
        # 创建临时文件
        temp_file = self.create_temp_csv_file(test_data, LOTTERY_TYPE_DLT)
        
        # 读取数据
        data_list = self.data_reader.read_csv(temp_file, LOTTERY_TYPE_DLT)
        
        # 验证数据
        validation_result = self.data_validator.validate_data_list(data_list)
        
        # 检查验证结果（数据读取器已经过滤了一些无效数据）
        self.assertGreaterEqual(validation_result['total_count'], 2)  # 至少有2条数据被读取
        self.assertGreaterEqual(validation_result['valid_count'], 2)  # 至少有2条有效
        self.assertGreaterEqual(validation_result['invalid_count'], 0)  # 可能有无效数据
        self.assertTrue(len(validation_result['errors']) > 0)
    
    def test_data_consistency_check(self):
        """测试数据一致性检查"""
        # 创建包含一致性问题的数据
        test_data = [
            ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"],
            ["24001", "2024-01-01", "1", "5", "15", "25", "35", "2", "8"],
            ["24001", "2024-01-03", "3", "7", "17", "27", "33", "1", "9"],  # 重复期号
            ["24003", "2024-01-02", "2", "8", "18", "28", "34", "3", "10"],  # 日期乱序
        ]
        
        # 创建临时文件
        temp_file = self.create_temp_csv_file(test_data, LOTTERY_TYPE_DLT)
        
        # 读取数据
        data_list = self.data_reader.read_csv(temp_file, LOTTERY_TYPE_DLT)
        
        # 验证数据
        validation_result = self.data_validator.validate_data_list(data_list)
        
        # 检查警告信息
        self.assertTrue(len(validation_result['warnings']) > 0)
        self.assertTrue(any('重复' in warning for warning in validation_result['warnings']))
        self.assertTrue(any('顺序' in warning for warning in validation_result['warnings']))
    
    def test_different_lottery_types(self):
        """测试不同彩票类型的处理"""
        # 测试双色球数据
        ssq_data = [
            ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "红球6", "蓝球"],
            ["24001", "2024-01-02", "1", "5", "15", "25", "30", "33", "8"],
            ["24002", "2024-01-04", "3", "7", "17", "27", "31", "32", "2"]
        ]
        
        ssq_file = self.create_temp_csv_file(ssq_data, LOTTERY_TYPE_SSQ)
        ssq_list = self.data_reader.read_csv(ssq_file, LOTTERY_TYPE_SSQ)
        ssq_validation = self.data_validator.validate_data_list(ssq_list)
        
        self.assertEqual(ssq_validation['valid_count'], 2)
        
        # 测试排列五数据
        pl5_data = [
            ["期号", "开奖日期", "万位", "千位", "百位", "十位", "个位"],
            ["24001", "2024-01-01", "1", "2", "3", "4", "5"],
            ["24002", "2024-01-02", "6", "7", "8", "9", "0"]
        ]
        
        pl5_file = self.create_temp_csv_file(pl5_data, LOTTERY_TYPE_PL5)
        pl5_list = self.data_reader.read_csv(pl5_file, LOTTERY_TYPE_PL5)
        pl5_validation = self.data_validator.validate_data_list(pl5_list)
        
        self.assertEqual(pl5_validation['valid_count'], 2)
    
    def test_validation_summary_generation(self):
        """测试验证结果摘要生成"""
        # 创建测试数据
        test_data = [
            ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"],
            ["24001", "2024-01-01", "1", "5", "15", "25", "35", "2", "8"],
            ["24002", "2024-01-03", "3", "7", "17", "27", "33", "1", "9"],
            ["invalid", "2024-01-05", "1", "2", "3", "", "", "", ""]  # 无效数据
        ]
        
        temp_file = self.create_temp_csv_file(test_data, LOTTERY_TYPE_DLT)
        data_list = self.data_reader.read_csv(temp_file, LOTTERY_TYPE_DLT)
        validation_result = self.data_validator.validate_data_list(data_list)
        
        # 生成摘要
        summary = self.data_validator.get_validation_summary(validation_result)
        
        # 检查摘要内容
        self.assertIn("数据验证结果", summary)
        self.assertIn("总数据量", summary)
        self.assertIn("有效数据", summary)
        self.assertIn("无效数据", summary)
        self.assertIn("日期范围", summary)
    
    def test_empty_data_handling(self):
        """测试空数据处理"""
        # 创建只有列名的文件
        temp_file = self.create_temp_csv_file([
            ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"]
        ], LOTTERY_TYPE_DLT)
        
        # 读取数据应该返回空列表
        data_list = self.data_reader.read_csv(temp_file, LOTTERY_TYPE_DLT)
        self.assertEqual(len(data_list), 0)
        
        # 验证空数据
        validation_result = self.data_validator.validate_data_list(data_list)
        self.assertEqual(validation_result['total_count'], 0)
        self.assertTrue(len(validation_result['errors']) > 0)
    
    def test_large_dataset_performance(self):
        """测试大数据集性能"""
        # 生成较大的测试数据集
        large_data = [
            ["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"]
        ]
        for i in range(100):
            large_data.append([
                f"24{i+1:03d}",
                f"2024-01-{(i%28)+1:02d}",
                str(1 + i % 5),
                str(5 + i % 5),
                str(15 + i % 5),
                str(25 + i % 5),
                str(31 + i % 5),
                str(1 + i % 3),
                str(8 + i % 3)
            ])
        
        temp_file = self.create_temp_csv_file(large_data, LOTTERY_TYPE_DLT)
        
        # 测试读取性能
        import time
        start_time = time.time()
        data_list = self.data_reader.read_csv(temp_file, LOTTERY_TYPE_DLT)
        read_time = time.time() - start_time
        
        # 测试验证性能
        start_time = time.time()
        validation_result = self.data_validator.validate_data_list(data_list)
        validation_time = time.time() - start_time
        
        # 性能检查（应该在合理时间内完成）
        self.assertLess(read_time, 5.0)  # 读取应在5秒内完成
        self.assertLess(validation_time, 10.0)  # 验证应在10秒内完成
        
        # 数据正确性检查
        self.assertEqual(len(data_list), 100)
        self.assertEqual(validation_result['total_count'], 100)


if __name__ == "__main__":
    # 运行集成测试
    unittest.main(verbosity=2)
