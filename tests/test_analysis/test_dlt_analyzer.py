#!/usr/bin/env python3
"""
大乐透分析器测试模块
测试 DLTAnalyzer 类的各种分析功能
"""

import pytest
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.analysis.dlt_analyzer import DLTAnalyzer
from src.data.models import LotteryData
from src.data.config import LOTTERY_TYPE_DLT


class TestDLTAnalyzer:
    """大乐透分析器基础功能测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = DLTAnalyzer()
        
        # 创建测试数据
        self.test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 5, 15, 25, 35, 2, 8], LOTTERY_TYPE_DLT),
            LotteryData("24002", datetime(2024, 1, 3), [3, 7, 17, 27, 33, 1, 9], LOTTERY_TYPE_DLT),
            LotteryData("24003", datetime(2024, 1, 6), [2, 8, 18, 28, 34, 3, 10], LOTTERY_TYPE_DLT),
            LotteryData("24004", datetime(2024, 1, 8), [4, 9, 19, 29, 31, 5, 11], LOTTERY_TYPE_DLT),
            LotteryData("24005", datetime(2024, 1, 10), [6, 11, 21, 30, 32, 4, 12], LOTTERY_TYPE_DLT)
        ]
        
        self.analyzer.load_data(self.test_data)
    
    def test_analyze_odd_even_distribution_front(self):
        """测试前区奇偶分析"""
        result = self.analyzer.analyze_odd_even_distribution('front')
        
        # 验证基本结构
        assert 'zone_type' in result
        assert 'zone_name' in result
        assert 'total_periods' in result
        assert 'pattern_statistics' in result
        assert 'odd_count_stats' in result
        assert 'even_count_stats' in result
        assert 'latest_pattern' in result
        assert 'recent_details' in result
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '前区'
        assert result['total_periods'] == 5
        
        # 验证详细结果
        assert len(result['recent_details']) == 5
        for detail in result['recent_details']:
            assert 'issue_number' in detail
            assert 'draw_date' in detail
            assert 'numbers' in detail
            assert 'pattern' in detail
            assert 'odd_count' in detail
            assert 'even_count' in detail
            assert len(detail['pattern']) == 5  # 前区5个号码
    
    def test_analyze_odd_even_distribution_back(self):
        """测试后区奇偶分析"""
        result = self.analyzer.analyze_odd_even_distribution('back')
        
        assert result['zone_type'] == 'back'
        assert result['zone_name'] == '后区'
        assert result['total_periods'] == 5
        
        # 验证详细结果
        for detail in result['recent_details']:
            assert len(detail['pattern']) == 2  # 后区2个号码
    
    def test_analyze_big_small_distribution_front(self):
        """测试前区大小分析"""
        result = self.analyzer.analyze_big_small_distribution('front')
        
        # 验证基本结构
        assert 'zone_type' in result
        assert 'zone_name' in result
        assert 'total_periods' in result
        assert 'pattern_statistics' in result
        assert 'big_count_stats' in result
        assert 'small_count_stats' in result
        assert 'latest_pattern' in result
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '前区'
        assert result['total_periods'] == 5
        
        # 验证详细结果
        for detail in result['recent_details']:
            assert len(detail['pattern']) == 5  # 前区5个号码
            assert detail['big_count'] + detail['small_count'] == 5
    
    def test_analyze_big_small_distribution_back(self):
        """测试后区大小分析"""
        result = self.analyzer.analyze_big_small_distribution('back')
        
        assert result['zone_type'] == 'back'
        assert result['zone_name'] == '后区'
        
        # 验证详细结果
        for detail in result['recent_details']:
            assert len(detail['pattern']) == 2  # 后区2个号码
            assert detail['big_count'] + detail['small_count'] == 2
    
    def test_analyze_zone_ratio_front(self):
        """测试前区分区比分析"""
        result = self.analyzer.analyze_zone_ratio('front')
        
        # 验证基本结构
        assert 'zone_type' in result
        assert 'zone_name' in result
        assert 'total_periods' in result
        assert 'zone_definitions' in result
        assert 'pattern_statistics' in result
        assert 'zone_totals' in result
        assert 'zone_averages' in result
        assert 'latest_pattern' in result
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '前区'
        assert result['total_periods'] == 5
        
        # 验证分区定义
        assert len(result['zone_definitions']) > 0
        for zone in result['zone_definitions']:
            assert 'name' in zone
            assert 'range' in zone
        
        # 验证详细结果
        for detail in result['recent_details']:
            assert 'zone_distribution' in detail
            assert isinstance(detail['zone_distribution'], dict)
    
    def test_analyze_zone_ratio_back(self):
        """测试后区分区比分析"""
        result = self.analyzer.analyze_zone_ratio('back')
        
        assert result['zone_type'] == 'back'
        assert result['zone_name'] == '后区'
        
        # 后区应该有较少的分区
        assert len(result['zone_definitions']) <= len(result['zone_definitions'])
    
    def test_calculate_number_intervals_front(self):
        """测试前区号码遗漏分析"""
        result = self.analyzer.calculate_number_intervals(15, 'front')
        
        # 验证基本结构
        assert 'number' in result
        assert 'zone_type' in result
        assert 'zone_name' in result
        assert 'total_periods' in result
        assert 'occurrence_count' in result
        assert 'occurrence_frequency' in result
        assert 'current_missing_periods' in result
        assert 'intervals' in result
        assert 'occurrences' in result
        
        assert result['number'] == 15
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '前区'
        assert result['total_periods'] == 5
        
        # 验证出现记录
        for occurrence in result['occurrences']:
            assert 'position' in occurrence
            assert 'issue_number' in occurrence
            assert 'draw_date' in occurrence
            assert 'numbers' in occurrence
            assert 15 in occurrence['numbers']
    
    def test_calculate_number_intervals_back(self):
        """测试后区号码遗漏分析"""
        result = self.analyzer.calculate_number_intervals(8, 'back')
        
        assert result['number'] == 8
        assert result['zone_type'] == 'back'
        assert result['zone_name'] == '后区'
    
    def test_calculate_all_numbers_missing_front(self):
        """测试前区所有号码遗漏统计"""
        result = self.analyzer.calculate_all_numbers_missing('front')
        
        # 验证基本结构
        assert 'zone_type' in result
        assert 'zone_name' in result
        assert 'total_numbers' in result
        assert 'total_periods' in result
        assert 'missing_statistics' in result
        assert 'frequency_statistics' in result
        assert 'numbers_by_missing' in result
        assert 'most_missing' in result
        assert 'least_missing' in result
        assert 'all_numbers_stats' in result
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '前区'
        assert result['total_numbers'] == 35  # 前区1-35
        assert result['total_periods'] == 5
        
        # 验证号码统计
        assert len(result['all_numbers_stats']) == 35
        for stat in result['all_numbers_stats']:
            assert 'number' in stat
            assert 'current_missing' in stat
            assert 'occurrence_count' in stat
            assert 'occurrence_frequency' in stat
            assert 1 <= stat['number'] <= 35
    
    def test_calculate_all_numbers_missing_back(self):
        """测试后区所有号码遗漏统计"""
        result = self.analyzer.calculate_all_numbers_missing('back')
        
        assert result['zone_type'] == 'back'
        assert result['zone_name'] == '后区'
        assert result['total_numbers'] == 12  # 后区1-12
        
        # 验证号码统计
        assert len(result['all_numbers_stats']) == 12
        for stat in result['all_numbers_stats']:
            assert 1 <= stat['number'] <= 12


class TestDLTAnalyzerEdgeCases:
    """大乐透分析器边界条件测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = DLTAnalyzer()
    
    def test_empty_data_analysis(self):
        """测试空数据分析"""
        with pytest.raises(ValueError, match="没有找到"):
            self.analyzer.load_data([])
    
    def test_invalid_zone_type(self):
        """测试无效区域类型"""
        test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 5, 15, 25, 35, 2, 8], LOTTERY_TYPE_DLT)
        ]
        self.analyzer.load_data(test_data)
        
        with pytest.raises(ValueError, match="zone_type 必须是"):
            self.analyzer.analyze_odd_even_distribution('invalid')
    
    def test_invalid_number_range_front(self):
        """测试前区无效号码范围"""
        test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 5, 15, 25, 35, 2, 8], LOTTERY_TYPE_DLT)
        ]
        self.analyzer.load_data(test_data)
        
        with pytest.raises(ValueError, match="前区号码必须在1-35之间"):
            self.analyzer.calculate_number_intervals(0, 'front')
        
        with pytest.raises(ValueError, match="前区号码必须在1-35之间"):
            self.analyzer.calculate_number_intervals(36, 'front')
    
    def test_invalid_number_range_back(self):
        """测试后区无效号码范围"""
        test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 5, 15, 25, 35, 2, 8], LOTTERY_TYPE_DLT)
        ]
        self.analyzer.load_data(test_data)
        
        with pytest.raises(ValueError, match="后区号码必须在1-12之间"):
            self.analyzer.calculate_number_intervals(0, 'back')
        
        with pytest.raises(ValueError, match="后区号码必须在1-12之间"):
            self.analyzer.calculate_number_intervals(13, 'back')
    
    def test_single_data_analysis(self):
        """测试单条数据分析"""
        test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 5, 15, 25, 35, 2, 8], LOTTERY_TYPE_DLT)
        ]
        self.analyzer.load_data(test_data)
        
        result = self.analyzer.analyze_odd_even_distribution('front')
        assert result['total_periods'] == 1
        assert len(result['recent_details']) == 1
        
        # 单条数据的间隔应该为空
        number_result = self.analyzer.calculate_number_intervals(15, 'front')
        assert number_result['intervals'] == []  # 只有一次出现，没有间隔


class TestDLTAnalyzerIntegration:
    """大乐透分析器集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = DLTAnalyzer()
        
        # 创建更多测试数据
        self.test_data = []
        for i in range(10):
            front_numbers = [1+i, 5+i, 15+i%10, 25-i%5, 35-i]
            back_numbers = [1+i%12, 2+i%11]
            # 确保号码在有效范围内
            front_numbers = [min(35, max(1, num)) for num in front_numbers]
            back_numbers = [min(12, max(1, num)) for num in back_numbers]
            
            self.test_data.append(
                LotteryData(
                    f"24{i+1:03d}",
                    datetime(2024, 1, i+1),
                    front_numbers + back_numbers,
                    LOTTERY_TYPE_DLT
                )
            )
        
        self.analyzer.load_data(self.test_data)
    
    def test_comprehensive_analysis(self):
        """测试综合分析功能"""
        # 测试所有分析功能
        analyses = [
            ('analyze_odd_even_distribution', 'front'),
            ('analyze_odd_even_distribution', 'back'),
            ('analyze_big_small_distribution', 'front'),
            ('analyze_big_small_distribution', 'back'),
            ('analyze_zone_ratio', 'front'),
            ('analyze_zone_ratio', 'back')
        ]
        
        for method_name, zone_type in analyses:
            method = getattr(self.analyzer, method_name)
            result = method(zone_type)
            
            assert result['total_periods'] == 10
            assert len(result['recent_details']) <= 20  # 最多20期
            assert result['latest_pattern'] != ""
    
    def test_number_analysis_consistency(self):
        """测试号码分析的一致性"""
        # 测试前区所有号码遗漏统计
        all_missing = self.analyzer.calculate_all_numbers_missing('front')
        
        # 验证单个号码分析与整体统计的一致性
        for stat in all_missing['all_numbers_stats'][:5]:  # 测试前5个号码
            number = stat['number']
            single_result = self.analyzer.calculate_number_intervals(number, 'front')
            
            assert single_result['occurrence_count'] == stat['occurrence_count']
            assert single_result['current_missing_periods'] == stat['current_missing']
            assert abs(single_result['occurrence_frequency'] - stat['occurrence_frequency']) < 0.001
    
    def test_pattern_interval_analysis(self):
        """测试模式间隔分析"""
        # 获取奇偶分析结果
        odd_even_result = self.analyzer.analyze_odd_even_distribution('front')
        
        if odd_even_result['most_common_patterns']:
            # 测试最常见模式的间隔分析
            common_pattern = odd_even_result['most_common_patterns'][0][0]
            interval_result = self.analyzer.calculate_odd_even_intervals(common_pattern, 'front')
            
            assert 'target_pattern' in interval_result
            assert 'occurrence_count' in interval_result
            assert 'intervals' in interval_result
            assert 'missing_periods' in interval_result
            assert interval_result['target_pattern'] == common_pattern


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
