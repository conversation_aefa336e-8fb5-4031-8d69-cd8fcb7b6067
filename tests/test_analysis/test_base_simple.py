#!/usr/bin/env python3
"""
基础分析器简单测试模块
测试 LotteryAnalyzer 基类的基本功能
"""

import pytest
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.analysis.dlt_analyzer import DLTAnalyzer
from src.data.models import LotteryData
from src.data.config import LOTTERY_TYPE_DLT


class TestLotteryAnalyzerBasics:
    """基础分析器功能测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = DLTAnalyzer()
        
        # 创建测试数据
        self.test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 5, 15, 25, 35, 2, 8], LOTTERY_TYPE_DLT),
            LotteryData("24002", datetime(2024, 1, 3), [3, 7, 17, 27, 33, 1, 9], LOTTERY_TYPE_DLT),
            LotteryData("24003", datetime(2024, 1, 6), [2, 8, 18, 28, 34, 3, 10], LOTTERY_TYPE_DLT)
        ]
    
    def test_analyzer_initialization(self):
        """测试分析器初始化"""
        assert self.analyzer.lottery_type == LOTTERY_TYPE_DLT
        assert len(self.analyzer.data_list) == 0
        assert self.analyzer.rules is not None
    
    def test_load_data_basic(self):
        """测试基本数据加载"""
        self.analyzer.load_data(self.test_data)
        
        assert len(self.analyzer.data_list) == 3
        assert self.analyzer.data_list[0].issue_number == "24001"
        assert self.analyzer.data_list[-1].issue_number == "24003"
    
    def test_get_data_count(self):
        """测试获取数据数量"""
        assert self.analyzer.get_data_count() == 0
        
        self.analyzer.load_data(self.test_data)
        assert self.analyzer.get_data_count() == 3
    
    def test_get_date_range(self):
        """测试获取日期范围"""
        self.analyzer.load_data(self.test_data)
        
        start_date, end_date = self.analyzer.get_date_range()
        assert start_date == datetime(2024, 1, 1)
        assert end_date == datetime(2024, 1, 6)
    
    def test_empty_data_handling(self):
        """测试空数据处理"""
        with pytest.raises(ValueError, match="没有找到"):
            self.analyzer.load_data([])
    
    def test_wrong_lottery_type_data(self):
        """测试错误彩票类型数据"""
        wrong_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 5, 15, 25, 30, 33, 8], "ssq")
        ]

        with pytest.raises(ValueError, match="没有找到"):
            self.analyzer.load_data(wrong_data)


class TestAnalysisMethodsExist:
    """测试分析方法是否存在"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = DLTAnalyzer()
        
        # 创建测试数据
        self.test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 5, 15, 25, 35, 2, 8], LOTTERY_TYPE_DLT),
            LotteryData("24002", datetime(2024, 1, 3), [3, 7, 17, 27, 33, 1, 9], LOTTERY_TYPE_DLT)
        ]
        
        self.analyzer.load_data(self.test_data)
    
    def test_odd_even_analysis_exists(self):
        """测试奇偶分析方法存在"""
        assert hasattr(self.analyzer, 'analyze_odd_even_distribution')
        
        # 测试方法可以调用
        result = self.analyzer.analyze_odd_even_distribution('front')
        assert isinstance(result, dict)
        assert 'zone_type' in result
    
    def test_big_small_analysis_exists(self):
        """测试大小分析方法存在"""
        assert hasattr(self.analyzer, 'analyze_big_small_distribution')
        
        # 测试方法可以调用
        result = self.analyzer.analyze_big_small_distribution('front')
        assert isinstance(result, dict)
        assert 'zone_type' in result
    
    def test_zone_ratio_analysis_exists(self):
        """测试分区比分析方法存在"""
        assert hasattr(self.analyzer, 'analyze_zone_ratio')
        
        # 测试方法可以调用
        result = self.analyzer.analyze_zone_ratio('front')
        assert isinstance(result, dict)
        assert 'zone_type' in result
    
    def test_number_intervals_exists(self):
        """测试号码遗漏分析方法存在"""
        assert hasattr(self.analyzer, 'calculate_number_intervals')
        
        # 测试方法可以调用
        result = self.analyzer.calculate_number_intervals(15, 'front')
        assert isinstance(result, dict)
        assert 'number' in result
    
    def test_all_numbers_missing_exists(self):
        """测试所有号码遗漏统计方法存在"""
        assert hasattr(self.analyzer, 'calculate_all_numbers_missing')
        
        # 测试方法可以调用
        result = self.analyzer.calculate_all_numbers_missing('front')
        assert isinstance(result, dict)
        assert 'zone_type' in result


class TestAnalysisResultStructure:
    """测试分析结果结构"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = DLTAnalyzer()
        
        # 创建更多测试数据
        self.test_data = []
        for i in range(5):
            self.test_data.append(
                LotteryData(
                    f"24{i+1:03d}",
                    datetime(2024, 1, i+1),
                    [1+i, 5+i, 15+i%10, 25-i%5, 35-i, 2+i%3, 8+i%3],
                    LOTTERY_TYPE_DLT
                )
            )
        
        self.analyzer.load_data(self.test_data)
    
    def test_odd_even_result_structure(self):
        """测试奇偶分析结果结构"""
        result = self.analyzer.analyze_odd_even_distribution('front')
        
        required_keys = [
            'zone_type', 'zone_name', 'total_periods',
            'pattern_statistics', 'latest_pattern', 'recent_details'
        ]
        
        for key in required_keys:
            assert key in result, f"缺少必需的键: {key}"
        
        assert result['zone_type'] == 'front'
        assert result['total_periods'] == 5
        assert isinstance(result['pattern_statistics'], dict)
        assert isinstance(result['recent_details'], list)
    
    def test_number_intervals_result_structure(self):
        """测试号码遗漏分析结果结构"""
        result = self.analyzer.calculate_number_intervals(15, 'front')
        
        required_keys = [
            'number', 'zone_type', 'zone_name', 'total_periods',
            'occurrence_count', 'occurrence_frequency', 'current_missing_periods',
            'intervals', 'occurrences'
        ]
        
        for key in required_keys:
            assert key in result, f"缺少必需的键: {key}"
        
        assert result['number'] == 15
        assert result['zone_type'] == 'front'
        assert result['total_periods'] == 5
        assert isinstance(result['intervals'], list)
        assert isinstance(result['occurrences'], list)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
