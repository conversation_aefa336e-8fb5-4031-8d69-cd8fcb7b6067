#!/usr/bin/env python3
"""
系统集成测试
测试完整的系统功能流程
"""

import unittest
import sys
import os
import tempfile
import csv
import time
from datetime import datetime
from unittest.mock import Mock, patch

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.data.reader import DataReader
from src.data.validator import DataValidator
from src.data.models import LotteryData
from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5
from src.analysis.dlt_analyzer import DLTAnalyzer
from src.analysis.ssq_analyzer import SSQAnalyzer
from src.analysis.pl5_analyzer import PL5Analyzer


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_files = []
        self.data_reader = DataReader()
        self.data_validator = DataValidator()
    
    def tearDown(self):
        """测试后清理"""
        # 清理临时文件
        for temp_file in self.temp_files:
            try:
                os.unlink(temp_file)
            except:
                pass
    
    def create_test_data_file(self, lottery_type: str, data_count: int = 50) -> str:
        """创建测试数据文件"""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8')
        self.temp_files.append(temp_file.name)
        
        writer = csv.writer(temp_file)
        
        if lottery_type == LOTTERY_TYPE_DLT:
            # 大乐透数据
            writer.writerow(["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "蓝球1", "蓝球2"])
            for i in range(data_count):
                writer.writerow([
                    f"24{i+1:03d}",
                    f"2024-01-{(i%28)+1:02d}",
                    str(1 + i % 5),
                    str(5 + i % 5),
                    str(15 + i % 5),
                    str(25 + i % 5),
                    str(31 + i % 5),
                    str(1 + i % 3),
                    str(8 + i % 3)
                ])
        elif lottery_type == LOTTERY_TYPE_SSQ:
            # 双色球数据
            writer.writerow(["期号", "开奖日期", "红球1", "红球2", "红球3", "红球4", "红球5", "红球6", "蓝球1"])
            for i in range(data_count):
                writer.writerow([
                    f"24{i+1:03d}",
                    f"2024-01-{(i%28)+1:02d}",
                    str(1 + i % 6),
                    str(7 + i % 6),
                    str(13 + i % 6),
                    str(19 + i % 6),
                    str(25 + i % 6),
                    str(31 + i % 3),
                    str(1 + i % 16)
                ])
        elif lottery_type == LOTTERY_TYPE_PL5:
            # 排列五数据（特殊格式）
            writer.writerow(["date", "period", "numbers", "sum_value"])
            for i in range(data_count):
                numbers = f"{i % 10} {(i+1) % 10} {(i+2) % 10} {(i+3) % 10} {(i+4) % 10}"
                sum_value = sum([i % 10, (i+1) % 10, (i+2) % 10, (i+3) % 10, (i+4) % 10])
                writer.writerow([
                    f"2024-01-{(i%28)+1:02d}",
                    f"24{i+1:03d}",
                    numbers,
                    str(sum_value)
                ])
        
        temp_file.close()
        return temp_file.name
    
    def test_complete_dlt_workflow(self):
        """测试大乐透完整工作流程"""
        print("\n测试大乐透完整工作流程...")
        
        # 1. 创建测试数据
        data_file = self.create_test_data_file(LOTTERY_TYPE_DLT, 100)
        
        # 2. 数据读取
        start_time = time.time()
        data_list = self.data_reader.read_csv(data_file, LOTTERY_TYPE_DLT)
        read_time = time.time() - start_time
        
        self.assertGreater(len(data_list), 0)
        self.assertLess(read_time, 5.0)  # 读取时间应在5秒内
        print(f"  数据读取: {len(data_list)}条数据，耗时{read_time:.2f}秒")
        
        # 3. 数据验证
        start_time = time.time()
        validation_result = self.data_validator.validate_data_list(data_list)
        validation_time = time.time() - start_time
        
        self.assertGreater(validation_result['valid_count'], 0)
        self.assertLess(validation_time, 10.0)  # 验证时间应在10秒内
        print(f"  数据验证: {validation_result['valid_count']}条有效，耗时{validation_time:.2f}秒")
        
        # 4. 分析器创建和数据加载
        analyzer = DLTAnalyzer()
        analyzer.load_data(data_list)
        
        # 5. 执行所有分析
        start_time = time.time()
        
        # 奇偶分析
        odd_even_front = analyzer.analyze_odd_even_distribution('front')
        odd_even_back = analyzer.analyze_odd_even_distribution('back')
        
        # 大小分析
        big_small_front = analyzer.analyze_big_small_distribution('front')
        big_small_back = analyzer.analyze_big_small_distribution('back')
        
        # 遗漏分析
        missing_front = analyzer.calculate_all_numbers_missing('front')
        missing_back = analyzer.calculate_all_numbers_missing('back')
        
        # 分区比分析
        zone_front = analyzer.analyze_zone_ratio('front')
        zone_back = analyzer.analyze_zone_ratio('back')
        
        analysis_time = time.time() - start_time
        
        # 6. 验证分析结果
        self.assertIn('total_periods', odd_even_front)
        self.assertIn('latest_pattern', odd_even_front)
        self.assertIn('total_periods', big_small_front)
        self.assertIn('total_numbers', missing_front)
        self.assertIn('total_periods', zone_front)
        
        self.assertLess(analysis_time, 30.0)  # 分析时间应在30秒内
        print(f"  分析执行: 8项分析完成，耗时{analysis_time:.2f}秒")
        
        print("  大乐透完整工作流程测试通过")
    
    def test_complete_ssq_workflow(self):
        """测试双色球完整工作流程"""
        print("\n测试双色球完整工作流程...")
        
        # 创建测试数据并执行完整流程
        data_file = self.create_test_data_file(LOTTERY_TYPE_SSQ, 50)
        
        # 数据处理
        data_list = self.data_reader.read_csv(data_file, LOTTERY_TYPE_SSQ)
        validation_result = self.data_validator.validate_data_list(data_list)
        
        # 分析执行
        analyzer = SSQAnalyzer()
        analyzer.load_data(data_list)
        
        # 执行主要分析
        odd_even_result = analyzer.analyze_odd_even_distribution('front')
        big_small_result = analyzer.analyze_big_small_distribution('front')
        missing_result = analyzer.calculate_all_numbers_missing('front')
        zone_result = analyzer.analyze_zone_ratio('front')
        
        # 验证结果
        self.assertGreater(odd_even_result['total_periods'], 0)
        self.assertGreater(big_small_result['total_periods'], 0)
        self.assertGreater(missing_result['total_numbers'], 0)
        self.assertGreater(zone_result['total_periods'], 0)
        
        print("  双色球完整工作流程测试通过")
    
    def test_complete_pl5_workflow(self):
        """测试排列五完整工作流程"""
        print("\n测试排列五完整工作流程...")
        
        # 创建测试数据并执行完整流程
        data_file = self.create_test_data_file(LOTTERY_TYPE_PL5, 50)
        
        # 数据处理
        data_list = self.data_reader.read_csv(data_file, LOTTERY_TYPE_PL5)
        validation_result = self.data_validator.validate_data_list(data_list)
        
        # 分析执行
        analyzer = PL5Analyzer()
        analyzer.load_data(data_list)
        
        # 执行主要分析
        odd_even_result = analyzer.analyze_odd_even_distribution()
        big_small_result = analyzer.analyze_big_small_distribution()
        missing_result = analyzer.calculate_all_numbers_missing()
        
        # 验证结果
        self.assertGreater(odd_even_result['total_periods'], 0)
        self.assertGreater(big_small_result['total_periods'], 0)
        self.assertGreater(missing_result['total_numbers'], 0)
        
        print("  排列五完整工作流程测试通过")
    
    def test_system_performance(self):
        """测试系统性能"""
        print("\n测试系统性能...")
        
        # 创建大数据集
        large_data_file = self.create_test_data_file(LOTTERY_TYPE_DLT, 1000)
        
        # 测试读取性能
        start_time = time.time()
        data_list = self.data_reader.read_csv(large_data_file, LOTTERY_TYPE_DLT)
        read_time = time.time() - start_time
        
        # 测试分析性能
        analyzer = DLTAnalyzer()
        analyzer.load_data(data_list)
        
        start_time = time.time()
        analyzer.analyze_odd_even_distribution('front')
        analyzer.analyze_big_small_distribution('front')
        analyzer.calculate_all_numbers_missing('front')
        analyzer.analyze_zone_ratio('front')
        analysis_time = time.time() - start_time
        
        # 性能要求
        self.assertLess(read_time, 10.0)  # 读取1000条数据应在10秒内
        self.assertLess(analysis_time, 60.0)  # 分析应在60秒内完成
        
        print(f"  大数据集性能: 读取{read_time:.2f}秒，分析{analysis_time:.2f}秒")
        print("  系统性能测试通过")
    
    def test_error_handling(self):
        """测试错误处理"""
        print("\n测试错误处理...")
        
        # 测试无效文件
        with self.assertRaises(FileNotFoundError):
            self.data_reader.read_csv("nonexistent.csv", LOTTERY_TYPE_DLT)
        
        # 测试无效数据
        analyzer = DLTAnalyzer()
        with self.assertRaises(ValueError):
            analyzer.load_data([])
        
        print("  错误处理测试通过")
    
    def test_memory_usage(self):
        """测试内存使用"""
        print("\n测试内存使用...")
        
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # 处理大量数据
        data_file = self.create_test_data_file(LOTTERY_TYPE_DLT, 500)
        data_list = self.data_reader.read_csv(data_file, LOTTERY_TYPE_DLT)
        
        analyzer = DLTAnalyzer()
        analyzer.load_data(data_list)
        
        # 执行多次分析
        for _ in range(10):
            analyzer.analyze_odd_even_distribution('front')
            analyzer.analyze_big_small_distribution('front')
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        # 内存增长应该合理（小于100MB）
        self.assertLess(memory_increase, 100)
        
        print(f"  内存使用: 初始{initial_memory:.1f}MB，最终{final_memory:.1f}MB，增长{memory_increase:.1f}MB")
        print("  内存使用测试通过")


if __name__ == "__main__":
    # 运行系统集成测试
    unittest.main(verbosity=2)
