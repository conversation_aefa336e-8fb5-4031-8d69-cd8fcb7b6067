#!/usr/bin/env python3
"""
配置模块测试
测试配置常量和工具函数
"""

import pytest
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.data.config import (
    LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5,
    get_big_small_boundary, get_zones
)


class TestLotteryTypes:
    """彩票类型常量测试"""
    
    def test_lottery_type_constants(self):
        """测试彩票类型常量"""
        assert LOTTERY_TYPE_DLT == "dlt"
        assert LOTTERY_TYPE_SSQ == "ssq"
        assert LOTTERY_TYPE_PL5 == "pl5"
    
    def test_lottery_types_unique(self):
        """测试彩票类型唯一性"""
        types = [LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5]
        assert len(types) == len(set(types))


class TestBigSmallBoundary:
    """大小分界点测试"""
    
    def test_dlt_boundaries(self):
        """测试大乐透大小分界点"""
        front_boundary = get_big_small_boundary(LOTTERY_TYPE_DLT, 'front')
        back_boundary = get_big_small_boundary(LOTTERY_TYPE_DLT, 'back')
        
        assert isinstance(front_boundary, int)
        assert isinstance(back_boundary, int)
        assert front_boundary > 0
        assert back_boundary > 0
        assert front_boundary <= 35  # 大乐透前区最大号码
        assert back_boundary <= 12   # 大乐透后区最大号码
    
    def test_ssq_boundaries(self):
        """测试双色球大小分界点"""
        front_boundary = get_big_small_boundary(LOTTERY_TYPE_SSQ, 'front')
        back_boundary = get_big_small_boundary(LOTTERY_TYPE_SSQ, 'back')
        
        assert isinstance(front_boundary, int)
        assert isinstance(back_boundary, int)
        assert front_boundary > 0
        assert back_boundary > 0
        assert front_boundary <= 33  # 双色球红球区最大号码
        assert back_boundary <= 16   # 双色球蓝球区最大号码
    
    def test_pl5_boundary(self):
        """测试排列五大小分界点"""
        boundary = get_big_small_boundary(LOTTERY_TYPE_PL5)
        
        assert isinstance(boundary, int)
        assert boundary >= 0
        assert boundary <= 9  # 排列五最大数字
    
    def test_invalid_lottery_type(self):
        """测试无效彩票类型"""
        with pytest.raises(ValueError, match="不支持的彩票类型"):
            get_big_small_boundary("invalid_type")
    
    def test_invalid_zone_type(self):
        """测试无效区域类型"""
        with pytest.raises(ValueError, match="不支持的区域类型"):
            get_big_small_boundary(LOTTERY_TYPE_DLT, "invalid_zone")
    
    def test_pl5_with_zone_type(self):
        """测试排列五使用区域类型"""
        # 排列五不区分前后区，应该忽略zone_type参数
        boundary1 = get_big_small_boundary(LOTTERY_TYPE_PL5)
        boundary2 = get_big_small_boundary(LOTTERY_TYPE_PL5, 'front')
        boundary3 = get_big_small_boundary(LOTTERY_TYPE_PL5, 'back')
        
        assert boundary1 == boundary2 == boundary3


class TestZones:
    """分区定义测试"""
    
    def test_dlt_zones(self):
        """测试大乐透分区定义"""
        front_zones = get_zones(LOTTERY_TYPE_DLT, 'front')
        back_zones = get_zones(LOTTERY_TYPE_DLT, 'back')
        
        # 检查前区分区
        assert isinstance(front_zones, list)
        assert len(front_zones) > 0
        for zone in front_zones:
            assert 'name' in zone
            assert 'range' in zone
            assert isinstance(zone['name'], str)
            assert isinstance(zone['range'], (list, tuple))
            assert len(zone['range']) == 2  # [start, end] or (start, end)
            assert zone['range'][0] <= zone['range'][1]
        
        # 检查后区分区
        assert isinstance(back_zones, list)
        assert len(back_zones) > 0
        for zone in back_zones:
            assert 'name' in zone
            assert 'range' in zone
            assert isinstance(zone['name'], str)
            assert isinstance(zone['range'], (list, tuple))
            assert len(zone['range']) == 2
            assert zone['range'][0] <= zone['range'][1]
    
    def test_ssq_zones(self):
        """测试双色球分区定义"""
        front_zones = get_zones(LOTTERY_TYPE_SSQ, 'front')
        back_zones = get_zones(LOTTERY_TYPE_SSQ, 'back')
        
        # 检查红球区分区
        assert isinstance(front_zones, list)
        assert len(front_zones) > 0
        for zone in front_zones:
            assert 'name' in zone
            assert 'range' in zone
            assert isinstance(zone['name'], str)
            assert isinstance(zone['range'], (list, tuple))
            assert len(zone['range']) == 2
            assert zone['range'][0] <= zone['range'][1]

        # 检查蓝球区分区
        assert isinstance(back_zones, list)
        assert len(back_zones) > 0
        for zone in back_zones:
            assert 'name' in zone
            assert 'range' in zone
            assert isinstance(zone['name'], str)
            assert isinstance(zone['range'], (list, tuple))
            assert len(zone['range']) == 2
            assert zone['range'][0] <= zone['range'][1]
    
    def test_pl5_zones(self):
        """测试排列五分区定义"""
        zones = get_zones(LOTTERY_TYPE_PL5)
        
        assert isinstance(zones, list)
        assert len(zones) > 0
        for zone in zones:
            assert 'name' in zone
            assert 'range' in zone
            assert isinstance(zone['name'], str)
            assert isinstance(zone['range'], (list, tuple))
            assert len(zone['range']) == 2
            assert zone['range'][0] <= zone['range'][1]
    
    def test_zone_coverage(self):
        """测试分区覆盖完整性"""
        # 测试大乐透前区分区覆盖1-35
        front_zones = get_zones(LOTTERY_TYPE_DLT, 'front')
        covered_numbers = set()
        for zone in front_zones:
            start, end = zone['range']
            covered_numbers.update(range(start, end + 1))
        
        expected_numbers = set(range(1, 36))  # 1-35
        assert covered_numbers == expected_numbers
        
        # 测试大乐透后区分区覆盖1-12
        back_zones = get_zones(LOTTERY_TYPE_DLT, 'back')
        covered_numbers = set()
        for zone in back_zones:
            start, end = zone['range']
            covered_numbers.update(range(start, end + 1))
        
        expected_numbers = set(range(1, 13))  # 1-12
        assert covered_numbers == expected_numbers
    
    def test_zone_no_overlap(self):
        """测试分区无重叠"""
        # 测试大乐透前区分区无重叠
        front_zones = get_zones(LOTTERY_TYPE_DLT, 'front')
        all_numbers = []
        for zone in front_zones:
            start, end = zone['range']
            zone_numbers = list(range(start, end + 1))
            all_numbers.extend(zone_numbers)
        
        # 检查无重复
        assert len(all_numbers) == len(set(all_numbers))
    
    def test_invalid_lottery_type_zones(self):
        """测试无效彩票类型的分区"""
        with pytest.raises(ValueError, match="不支持的彩票类型"):
            get_zones("invalid_type")
    
    def test_invalid_zone_type_zones(self):
        """测试无效区域类型的分区"""
        with pytest.raises(ValueError, match="不支持的区域类型"):
            get_zones(LOTTERY_TYPE_DLT, "invalid_zone")


class TestConfigIntegration:
    """配置模块集成测试"""
    
    def test_boundary_zone_consistency(self):
        """测试大小分界点与分区定义的一致性"""
        # 大乐透前区
        boundary = get_big_small_boundary(LOTTERY_TYPE_DLT, 'front')
        zones = get_zones(LOTTERY_TYPE_DLT, 'front')
        
        # 分界点应该在有效范围内
        min_number = min(zone['range'][0] for zone in zones)
        max_number = max(zone['range'][1] for zone in zones)
        assert min_number <= boundary <= max_number
    
    def test_all_configs_accessible(self):
        """测试所有配置都可访问"""
        # 测试所有彩票类型的大小分界点
        for lottery_type in [LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ]:
            for zone_type in ['front', 'back']:
                boundary = get_big_small_boundary(lottery_type, zone_type)
                assert isinstance(boundary, int)
        
        # 排列五
        boundary = get_big_small_boundary(LOTTERY_TYPE_PL5)
        assert isinstance(boundary, int)
        
        # 测试所有彩票类型的分区定义
        for lottery_type in [LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ]:
            for zone_type in ['front', 'back']:
                zones = get_zones(lottery_type, zone_type)
                assert isinstance(zones, list)
                assert len(zones) > 0
        
        # 排列五
        zones = get_zones(LOTTERY_TYPE_PL5)
        assert isinstance(zones, list)
        assert len(zones) > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
