#!/usr/bin/env python3
"""
基础分析器测试模块
测试 BaseAnalyzer 类的各种功能
"""

import pytest
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.analysis.base import LotteryAnalyzer
from src.data.models import LotteryData
from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5


class TestLotteryAnalyzer(LotteryAnalyzer):
    """测试用的具体分析器实现"""

    def __init__(self):
        super().__init__(LOTTERY_TYPE_DLT)

    def analyze_odd_even_distribution(self, zone_type='front'):
        return {"test": "implementation"}

    def analyze_big_small_distribution(self, zone_type='front'):
        return {"test": "implementation"}

    def analyze_zone_ratio(self, zone_type='front'):
        return {"test": "implementation"}

    def calculate_number_intervals(self, number, zone_type='front'):
        return {"test": "implementation"}


class TestBaseAnalyzer:
    """BaseAnalyzer 基础功能测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = TestLotteryAnalyzer()
        
        # 创建测试数据
        self.test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 5, 15, 25, 35, 2, 8], LOTTERY_TYPE_DLT),
            LotteryData("24002", datetime(2024, 1, 3), [3, 7, 17, 27, 33, 1, 9], LOTTERY_TYPE_DLT),
            LotteryData("24003", datetime(2024, 1, 6), [2, 8, 18, 28, 34, 3, 10], LOTTERY_TYPE_DLT),
            LotteryData("24004", datetime(2024, 1, 8), [4, 9, 19, 29, 31, 5, 11], LOTTERY_TYPE_DLT),
            LotteryData("24005", datetime(2024, 1, 10), [6, 11, 21, 30, 32, 4, 12], LOTTERY_TYPE_DLT)
        ]
    
    def test_load_data(self):
        """测试数据加载"""
        # 确保测试数据的彩票类型匹配
        test_data_dlt = []
        for data in self.test_data:
            test_data_dlt.append(LotteryData(
                data.issue_number,
                data.draw_date,
                data.numbers,
                LOTTERY_TYPE_DLT  # 确保类型匹配
            ))

        self.analyzer.load_data(test_data_dlt)

        assert len(self.analyzer.data_list) == 5
        assert self.analyzer.data_list[0].issue_number == "24001"
        assert self.analyzer.data_list[-1].issue_number == "24005"
    
    def test_load_empty_data(self):
        """测试加载空数据"""
        with pytest.raises(ValueError, match="没有找到"):
            self.analyzer.load_data([])

    def test_load_none_data(self):
        """测试加载None数据"""
        with pytest.raises((ValueError, TypeError)):
            self.analyzer.load_data(None)
    
    def test_calculate_pattern_statistics(self):
        """测试模式统计计算"""
        patterns = ["101", "010", "101", "110", "101"]
        stats = self.analyzer.calculate_pattern_statistics(patterns)
        
        assert "101" in stats
        assert "010" in stats
        assert "110" in stats
        
        assert stats["101"]["count"] == 3
        assert stats["010"]["count"] == 1
        assert stats["110"]["count"] == 1
        
        assert stats["101"]["frequency"] == 0.6
        assert stats["010"]["frequency"] == 0.2
        assert stats["110"]["frequency"] == 0.2
    
    def test_calculate_pattern_statistics_empty(self):
        """测试空模式列表的统计"""
        stats = self.analyzer.calculate_pattern_statistics([])
        assert stats == {}
    
    def test_find_pattern_intervals(self):
        """测试模式间隔查找"""
        patterns = ["101", "010", "101", "110", "101", "010", "101"]
        intervals = self.analyzer.find_pattern_intervals(patterns, "101")
        
        # "101"出现在位置0, 2, 4, 6，间隔为2, 2, 2
        assert intervals == [2, 2, 2]
    
    def test_find_pattern_intervals_not_found(self):
        """测试查找不存在的模式"""
        patterns = ["101", "010", "110"]
        intervals = self.analyzer.find_pattern_intervals(patterns, "111")
        assert intervals == []
    
    def test_find_pattern_intervals_single_occurrence(self):
        """测试只出现一次的模式"""
        patterns = ["101", "010", "110"]
        intervals = self.analyzer.find_pattern_intervals(patterns, "101")
        assert intervals == []
    
    def test_get_missing_periods(self):
        """测试遗漏期数计算"""
        patterns = ["101", "010", "101", "110", "010"]
        missing = self.analyzer.get_missing_periods(patterns, "101")
        
        # "101"最后出现在位置2，总长度5，遗漏期数为5-1-2=2
        assert missing == 2
    
    def test_get_missing_periods_not_found(self):
        """测试不存在模式的遗漏期数"""
        patterns = ["101", "010", "110"]
        missing = self.analyzer.get_missing_periods(patterns, "111")
        
        # 模式不存在，遗漏期数为总长度
        assert missing == 3
    
    def test_get_missing_periods_latest(self):
        """测试最新期出现的模式遗漏期数"""
        patterns = ["101", "010", "110"]
        missing = self.analyzer.get_missing_periods(patterns, "110")
        
        # "110"在最后一期出现，遗漏期数为0
        assert missing == 0


class TestBaseAnalyzerEdgeCases:
    """BaseAnalyzer 边界条件测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = TestLotteryAnalyzer()
    
    def test_pattern_statistics_with_none_values(self):
        """测试包含None值的模式统计"""
        patterns = ["101", None, "010", "101"]
        # 应该过滤掉None值
        stats = self.analyzer.calculate_pattern_statistics(patterns)
        
        assert "101" in stats
        assert "010" in stats
        assert None not in stats
        assert stats["101"]["count"] == 2
    
    def test_pattern_statistics_with_empty_strings(self):
        """测试包含空字符串的模式统计"""
        patterns = ["101", "", "010", "101"]
        stats = self.analyzer.calculate_pattern_statistics(patterns)
        
        # 空字符串应该被统计
        assert "" in stats
        assert stats[""]["count"] == 1
    
    def test_find_intervals_with_consecutive_patterns(self):
        """测试连续出现的模式间隔"""
        patterns = ["101", "101", "101", "010"]
        intervals = self.analyzer.find_pattern_intervals(patterns, "101")
        
        # 连续出现的间隔为1
        assert intervals == [1, 1]
    
    def test_large_pattern_list_performance(self):
        """测试大数据量的性能"""
        import time
        
        # 创建大量模式数据
        patterns = ["101", "010", "110"] * 1000  # 3000个模式
        
        start_time = time.time()
        stats = self.analyzer.calculate_pattern_statistics(patterns)
        end_time = time.time()
        
        # 验证结果正确性
        assert stats["101"]["count"] == 1000
        assert stats["010"]["count"] == 1000
        assert stats["110"]["count"] == 1000
        
        # 性能要求：处理3000个模式应在1秒内完成
        assert end_time - start_time < 1.0
    
    def test_unicode_patterns(self):
        """测试Unicode模式"""
        patterns = ["奇偶", "大小", "奇偶", "分区"]
        stats = self.analyzer.calculate_pattern_statistics(patterns)
        
        assert "奇偶" in stats
        assert "大小" in stats
        assert "分区" in stats
        assert stats["奇偶"]["count"] == 2


class TestBaseAnalyzerIntegration:
    """BaseAnalyzer 集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = TestLotteryAnalyzer()
        
        # 创建更多测试数据
        self.test_data = []
        for i in range(20):
            self.test_data.append(
                LotteryData(
                    f"24{i+1:03d}",
                    datetime(2024, 1, i+1),
                    [1+i%5, 5+i%5, 15+i%5, 25+i%5, 35-i%5, 2+i%3, 8+i%3],
                    LOTTERY_TYPE_DLT
                )
            )
    
    def test_full_workflow(self):
        """测试完整工作流程"""
        # 1. 加载数据
        self.analyzer.load_data(self.test_data)
        assert len(self.analyzer.data_list) == 20
        
        # 2. 生成模式（模拟奇偶模式）
        patterns = []
        for data in self.analyzer.data_list:
            front_numbers = data.get_front_numbers()
            pattern = "".join("1" if num % 2 == 1 else "0" for num in front_numbers)
            patterns.append(pattern)
        
        # 3. 计算统计
        stats = self.analyzer.calculate_pattern_statistics(patterns)
        assert len(stats) > 0
        
        # 4. 查找间隔
        if patterns:
            first_pattern = patterns[0]
            intervals = self.analyzer.find_pattern_intervals(patterns, first_pattern)
            missing = self.analyzer.get_missing_periods(patterns, first_pattern)
            
            assert isinstance(intervals, list)
            assert isinstance(missing, int)
            assert missing >= 0
    
    def test_data_validation(self):
        """测试数据验证"""
        # 测试无效数据类型
        with pytest.raises(ValueError):
            self.analyzer.load_data("invalid_data")
        
        with pytest.raises(ValueError):
            self.analyzer.load_data(123)
        
        # 测试包含无效元素的列表
        invalid_data = [self.test_data[0], "invalid", self.test_data[1]]
        with pytest.raises(ValueError):
            self.analyzer.load_data(invalid_data)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
