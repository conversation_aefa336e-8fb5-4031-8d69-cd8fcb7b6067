#!/usr/bin/env python3
"""
数据处理模块测试运行器
运行所有数据处理相关的测试
"""

import sys
import os
import pytest
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_data_tests():
    """运行数据处理模块的所有测试"""
    print("=" * 60)
    print("数据处理模块测试")
    print("=" * 60)
    
    test_dir = Path(__file__).parent / "test_data"
    
    # 测试文件列表
    test_files = [
        "test_models.py",
        "test_reader.py", 
        "test_config.py"
    ]
    
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    start_time = time.time()
    
    for test_file in test_files:
        test_path = test_dir / test_file
        if not test_path.exists():
            print(f"❌ 测试文件不存在: {test_file}")
            continue
        
        print(f"\n🧪 运行测试: {test_file}")
        print("-" * 40)
        
        # 运行单个测试文件
        result = pytest.main([
            str(test_path),
            "-v",
            "--tb=short",
            "--no-header",
            "--quiet"
        ])
        
        if result == 0:
            print(f"✅ {test_file} - 所有测试通过")
            passed_tests += 1
        else:
            print(f"❌ {test_file} - 部分测试失败")
            failed_tests += 1
        
        total_tests += 1
    
    end_time = time.time()
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"总测试文件数: {total_tests}")
    print(f"通过的文件数: {passed_tests}")
    print(f"失败的文件数: {failed_tests}")
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    
    if failed_tests == 0:
        print("🎉 所有数据处理模块测试通过！")
        return True
    else:
        print("⚠️  部分数据处理模块测试失败！")
        return False


def run_detailed_tests():
    """运行详细的测试报告"""
    print("=" * 60)
    print("数据处理模块详细测试报告")
    print("=" * 60)
    
    test_dir = Path(__file__).parent / "test_data"
    
    # 运行所有测试并生成详细报告
    result = pytest.main([
        str(test_dir),
        "-v",
        "--tb=long",
        "--show-capture=all",
        "--durations=10"
    ])
    
    return result == 0


def run_coverage_tests():
    """运行测试覆盖率分析"""
    try:
        import coverage
    except ImportError:
        print("❌ 需要安装 coverage 包来运行覆盖率测试")
        print("请运行: pip install coverage")
        return False
    
    print("=" * 60)
    print("数据处理模块测试覆盖率分析")
    print("=" * 60)
    
    test_dir = Path(__file__).parent / "test_data"
    src_dir = project_root / "src" / "data"
    
    # 运行覆盖率测试
    result = pytest.main([
        str(test_dir),
        f"--cov={src_dir}",
        "--cov-report=term-missing",
        "--cov-report=html:tests/coverage_html",
        "-v"
    ])
    
    if result == 0:
        print("\n📊 覆盖率报告已生成到 tests/coverage_html/")
    
    return result == 0


def check_test_environment():
    """检查测试环境"""
    print("🔍 检查测试环境...")
    
    # 检查必要的模块
    required_modules = ['pytest', 'src.data.models', 'src.data.reader', 'src.data.config']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            missing_modules.append(module)
    
    # 检查测试数据文件
    test_data_dir = Path(__file__).parent / "test_data"
    required_files = [
        "sample_dlt_data.csv",
        "sample_ssq_data.csv", 
        "sample_pl5_data.csv",
        "invalid_data.csv"
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = test_data_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name}")
            missing_files.append(file_name)
    
    if missing_modules or missing_files:
        print("\n⚠️  测试环境不完整，可能影响测试结果")
        return False
    else:
        print("\n✅ 测试环境检查通过")
        return True


def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "detailed":
            return run_detailed_tests()
        elif command == "coverage":
            return run_coverage_tests()
        elif command == "check":
            return check_test_environment()
        elif command == "help":
            print("数据处理模块测试运行器")
            print("用法:")
            print("  python run_data_tests.py          # 运行基本测试")
            print("  python run_data_tests.py detailed # 运行详细测试")
            print("  python run_data_tests.py coverage # 运行覆盖率测试")
            print("  python run_data_tests.py check    # 检查测试环境")
            print("  python run_data_tests.py help     # 显示帮助")
            return True
        else:
            print(f"❌ 未知命令: {command}")
            print("使用 'help' 查看可用命令")
            return False
    else:
        # 默认运行基本测试
        check_test_environment()
        return run_data_tests()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
