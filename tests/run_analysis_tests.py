#!/usr/bin/env python3
"""
分析模块测试运行器
运行所有分析模块相关的测试
"""

import sys
import os
import pytest
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def run_analysis_tests():
    """运行分析模块的所有测试"""
    print("=" * 60)
    print("分析模块测试")
    print("=" * 60)
    
    test_dir = Path(__file__).parent / "test_analysis"
    
    # 测试文件列表
    test_files = [
        "test_base.py",
        "test_utils.py",
        "test_dlt_analyzer.py",
        "test_ssq_analyzer.py",
        "test_pl5_analyzer.py"
    ]
    
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    start_time = time.time()
    
    for test_file in test_files:
        test_path = test_dir / test_file
        if not test_path.exists():
            print(f"❌ 测试文件不存在: {test_file}")
            continue
        
        print(f"\n🧪 运行测试: {test_file}")
        print("-" * 40)
        
        # 运行单个测试文件
        result = pytest.main([
            str(test_path),
            "-v",
            "--tb=short",
            "--no-header",
            "--quiet"
        ])
        
        if result == 0:
            print(f"✅ {test_file} - 所有测试通过")
            passed_tests += 1
        else:
            print(f"❌ {test_file} - 部分测试失败")
            failed_tests += 1
        
        total_tests += 1
    
    end_time = time.time()
    
    # 输出总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    print(f"总测试文件数: {total_tests}")
    print(f"通过的文件数: {passed_tests}")
    print(f"失败的文件数: {failed_tests}")
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    
    if failed_tests == 0:
        print("🎉 所有分析模块测试通过！")
        return True
    else:
        print("⚠️  部分分析模块测试失败！")
        return False


def run_detailed_tests():
    """运行详细的测试报告"""
    print("=" * 60)
    print("分析模块详细测试报告")
    print("=" * 60)
    
    test_dir = Path(__file__).parent / "test_analysis"
    
    # 运行所有测试并生成详细报告
    result = pytest.main([
        str(test_dir),
        "-v",
        "--tb=long",
        "--show-capture=all",
        "--durations=10"
    ])
    
    return result == 0


def run_coverage_tests():
    """运行测试覆盖率分析"""
    try:
        import coverage
    except ImportError:
        print("❌ 需要安装 coverage 包来运行覆盖率测试")
        print("请运行: pip install coverage")
        return False
    
    print("=" * 60)
    print("分析模块测试覆盖率分析")
    print("=" * 60)
    
    test_dir = Path(__file__).parent / "test_analysis"
    src_dir = project_root / "src" / "analysis"
    
    # 运行覆盖率测试
    result = pytest.main([
        str(test_dir),
        f"--cov={src_dir}",
        "--cov-report=term-missing",
        "--cov-report=html:tests/analysis_coverage_html",
        "-v"
    ])
    
    if result == 0:
        print("\n📊 覆盖率报告已生成到 tests/analysis_coverage_html/")
    
    return result == 0


def run_performance_tests():
    """运行性能测试"""
    print("=" * 60)
    print("分析模块性能测试")
    print("=" * 60)
    
    try:
        # 导入分析器
        from src.analysis.dlt_analyzer import DLTAnalyzer
        from src.analysis.ssq_analyzer import SSQAnalyzer
        from src.analysis.pl5_analyzer import PL5Analyzer
        from src.data.models import LotteryData
        from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5
        from datetime import datetime
        
        # 创建大量测试数据
        print("🔄 创建测试数据...")
        
        # 大乐透数据
        dlt_data = []
        for i in range(1000):
            front_numbers = [1+i%35, 5+i%30, 15+i%20, 25+i%10, 35-i%5]
            back_numbers = [1+i%12, 2+i%11]
            front_numbers = [min(35, max(1, num)) for num in front_numbers]
            back_numbers = [min(12, max(1, num)) for num in back_numbers]
            
            dlt_data.append(
                LotteryData(
                    f"24{i+1:03d}",
                    datetime(2024, 1, (i%30)+1),
                    LOTTERY_TYPE_DLT,
                    front_numbers + back_numbers
                )
            )
        
        # 测试大乐透分析器性能
        print("🧪 测试大乐透分析器性能...")
        dlt_analyzer = DLTAnalyzer()
        
        start_time = time.time()
        dlt_analyzer.load_data(dlt_data)
        load_time = time.time() - start_time
        
        start_time = time.time()
        dlt_analyzer.analyze_odd_even_distribution('front')
        odd_even_time = time.time() - start_time
        
        start_time = time.time()
        dlt_analyzer.analyze_big_small_distribution('front')
        big_small_time = time.time() - start_time
        
        start_time = time.time()
        dlt_analyzer.calculate_all_numbers_missing('front')
        missing_time = time.time() - start_time
        
        print(f"  数据加载: {load_time:.3f} 秒")
        print(f"  奇偶分析: {odd_even_time:.3f} 秒")
        print(f"  大小分析: {big_small_time:.3f} 秒")
        print(f"  遗漏分析: {missing_time:.3f} 秒")
        
        # 性能要求检查
        performance_ok = True
        if load_time > 1.0:
            print("⚠️  数据加载性能较慢")
            performance_ok = False
        if odd_even_time > 2.0:
            print("⚠️  奇偶分析性能较慢")
            performance_ok = False
        if big_small_time > 2.0:
            print("⚠️  大小分析性能较慢")
            performance_ok = False
        if missing_time > 5.0:
            print("⚠️  遗漏分析性能较慢")
            performance_ok = False
        
        if performance_ok:
            print("✅ 性能测试通过")
        else:
            print("❌ 性能测试未达到预期")
        
        return performance_ok
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_test_environment():
    """检查测试环境"""
    print("🔍 检查测试环境...")
    
    # 检查必要的模块
    required_modules = [
        'pytest', 
        'src.analysis.base',
        'src.analysis.utils',
        'src.analysis.dlt_analyzer',
        'src.analysis.ssq_analyzer',
        'src.analysis.pl5_analyzer'
    ]
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")
            missing_modules.append(module)
    
    # 检查测试文件
    test_analysis_dir = Path(__file__).parent / "test_analysis"
    required_files = [
        "test_base.py",
        "test_utils.py",
        "test_dlt_analyzer.py",
        "test_ssq_analyzer.py",
        "test_pl5_analyzer.py"
    ]
    
    missing_files = []
    for file_name in required_files:
        file_path = test_analysis_dir / file_name
        if file_path.exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name}")
            missing_files.append(file_name)
    
    if missing_modules or missing_files:
        print("\n⚠️  测试环境不完整，可能影响测试结果")
        return False
    else:
        print("\n✅ 测试环境检查通过")
        return True


def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "detailed":
            return run_detailed_tests()
        elif command == "coverage":
            return run_coverage_tests()
        elif command == "performance":
            return run_performance_tests()
        elif command == "check":
            return check_test_environment()
        elif command == "help":
            print("分析模块测试运行器")
            print("用法:")
            print("  python run_analysis_tests.py            # 运行基本测试")
            print("  python run_analysis_tests.py detailed   # 运行详细测试")
            print("  python run_analysis_tests.py coverage   # 运行覆盖率测试")
            print("  python run_analysis_tests.py performance # 运行性能测试")
            print("  python run_analysis_tests.py check      # 检查测试环境")
            print("  python run_analysis_tests.py help       # 显示帮助")
            return True
        else:
            print(f"❌ 未知命令: {command}")
            print("使用 'help' 查看可用命令")
            return False
    else:
        # 默认运行基本测试
        check_test_environment()
        return run_analysis_tests()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
