"""
分析工具函数

提供彩票分析中常用的工具函数，包括日期处理、数据统计、模式匹配等功能。
"""

from typing import List, Dict, Any, Tuple, Optional
from datetime import datetime, timedelta
import statistics
from collections import Counter

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from data.models import LotteryData
from data.config import get_big_small_boundary, get_zones


def is_odd(number: int) -> bool:
    """
    判断数字是否为奇数
    
    Args:
        number (int): 数字
        
    Returns:
        bool: 是否为奇数
    """
    return number % 2 == 1


def is_big(number: int, lottery_type: str, zone_type: str = 'front') -> bool:
    """
    判断数字是否为大号

    Args:
        number (int): 数字
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型

    Returns:
        bool: 是否为大号
    """
    from src.data.config import LOTTERY_TYPE_PL5
    boundary = get_big_small_boundary(lottery_type, zone_type)

    # 排列五使用大于等于的规则，其他彩票使用大于的规则
    if lottery_type == LOTTERY_TYPE_PL5:
        return number >= boundary
    else:
        return number > boundary


def generate_odd_even_pattern(numbers: List[int]) -> str:
    """
    生成奇偶模式代码
    
    Args:
        numbers (List[int]): 号码列表
        
    Returns:
        str: 奇偶模式代码 (1表示奇数，0表示偶数)
    """
    return ''.join('1' if is_odd(num) else '0' for num in numbers)


def generate_big_small_pattern(numbers: List[int], lottery_type: str, zone_type: str = 'front') -> str:
    """
    生成大小模式代码
    
    Args:
        numbers (List[int]): 号码列表
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型
        
    Returns:
        str: 大小模式代码 (1表示大号，0表示小号)
    """
    return ''.join('1' if is_big(num, lottery_type, zone_type) else '0' for num in numbers)


def count_odd_even(numbers: List[int]) -> Tuple[int, int]:
    """
    统计奇偶数量
    
    Args:
        numbers (List[int]): 号码列表
        
    Returns:
        Tuple[int, int]: (奇数数量, 偶数数量)
    """
    odd_count = sum(1 for num in numbers if is_odd(num))
    even_count = len(numbers) - odd_count
    return odd_count, even_count


def count_big_small(numbers: List[int], lottery_type: str, zone_type: str = 'front') -> Tuple[int, int]:
    """
    统计大小号数量
    
    Args:
        numbers (List[int]): 号码列表
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型
        
    Returns:
        Tuple[int, int]: (大号数量, 小号数量)
    """
    big_count = sum(1 for num in numbers if is_big(num, lottery_type, zone_type))
    small_count = len(numbers) - big_count
    return big_count, small_count


def get_zone_for_number(number: int, lottery_type: str, zone_type: str = 'front') -> Optional[str]:
    """
    获取号码所属的分区
    
    Args:
        number (int): 号码
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型
        
    Returns:
        Optional[str]: 分区名称，如果不在任何分区则返回None
    """
    zones = get_zones(lottery_type, zone_type)
    
    for zone in zones:
        start, end = zone['range']
        if start <= number <= end:
            return zone['name']
    
    return None


def count_zone_distribution(numbers: List[int], lottery_type: str, zone_type: str = 'front') -> Dict[str, int]:
    """
    统计号码在各分区的分布
    
    Args:
        numbers (List[int]): 号码列表
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型
        
    Returns:
        Dict[str, int]: 各分区的号码数量
    """
    zones = get_zones(lottery_type, zone_type)
    zone_count = {zone['name']: 0 for zone in zones}
    
    for number in numbers:
        zone_name = get_zone_for_number(number, lottery_type, zone_type)
        if zone_name and zone_name in zone_count:
            zone_count[zone_name] += 1
    
    return zone_count


def generate_zone_ratio_pattern(numbers: List[int], lottery_type: str, zone_type: str = 'front') -> str:
    """
    生成分区比模式代码
    
    Args:
        numbers (List[int]): 号码列表
        lottery_type (str): 彩票类型
        zone_type (str): 区域类型
        
    Returns:
        str: 分区比模式代码 (如 "2:1:2:0:1:1:0" 表示各分区的号码数量)
    """
    zone_count = count_zone_distribution(numbers, lottery_type, zone_type)
    zones = get_zones(lottery_type, zone_type)
    
    # 按分区顺序生成比例字符串
    ratios = [str(zone_count.get(zone['name'], 0)) for zone in zones]
    return ':'.join(ratios)


def calculate_statistics(values: List[float]) -> Dict[str, float]:
    """
    计算数值列表的统计信息
    
    Args:
        values (List[float]): 数值列表
        
    Returns:
        Dict[str, float]: 统计信息
    """
    if not values:
        return {
            'count': 0,
            'sum': 0,
            'mean': 0,
            'median': 0,
            'mode': 0,
            'min': 0,
            'max': 0,
            'std': 0
        }
    
    return {
        'count': len(values),
        'sum': sum(values),
        'mean': statistics.mean(values),
        'median': statistics.median(values),
        'mode': statistics.mode(values) if len(set(values)) < len(values) else values[0],
        'min': min(values),
        'max': max(values),
        'std': statistics.stdev(values) if len(values) > 1 else 0
    }


def find_consecutive_patterns(pattern_list: List[str], pattern: str, min_length: int = 2) -> List[Tuple[int, int]]:
    """
    查找连续出现的模式
    
    Args:
        pattern_list (List[str]): 模式列表
        pattern (str): 目标模式
        min_length (int): 最小连续长度
        
    Returns:
        List[Tuple[int, int]]: 连续出现的起始和结束位置列表
    """
    consecutive_ranges = []
    start = -1
    
    for i, p in enumerate(pattern_list):
        if p == pattern:
            if start == -1:
                start = i
        else:
            if start != -1:
                length = i - start
                if length >= min_length:
                    consecutive_ranges.append((start, i - 1))
                start = -1
    
    # 检查最后一个连续序列
    if start != -1:
        length = len(pattern_list) - start
        if length >= min_length:
            consecutive_ranges.append((start, len(pattern_list) - 1))
    
    return consecutive_ranges


def get_frequency_ranking(items: List[Any]) -> List[Tuple[Any, int]]:
    """
    获取项目的频率排名
    
    Args:
        items (List[Any]): 项目列表
        
    Returns:
        List[Tuple[Any, int]]: 按频率排序的 (项目, 频率) 列表
    """
    counter = Counter(items)
    return counter.most_common()


def format_date_range(start_date: datetime, end_date: datetime) -> str:
    """
    格式化日期范围
    
    Args:
        start_date (datetime): 开始日期
        end_date (datetime): 结束日期
        
    Returns:
        str: 格式化的日期范围字符串
    """
    return f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}"


def calculate_days_between(start_date: datetime, end_date: datetime) -> int:
    """
    计算两个日期之间的天数
    
    Args:
        start_date (datetime): 开始日期
        end_date (datetime): 结束日期
        
    Returns:
        int: 天数差
    """
    return (end_date - start_date).days


def group_data_by_period(data_list: List[LotteryData], period_days: int = 30) -> Dict[str, List[LotteryData]]:
    """
    按时间周期分组数据
    
    Args:
        data_list (List[LotteryData]): 数据列表
        period_days (int): 周期天数
        
    Returns:
        Dict[str, List[LotteryData]]: 按周期分组的数据
    """
    if not data_list:
        return {}
    
    # 按日期排序
    sorted_data = sorted(data_list, key=lambda x: x.draw_date)
    
    groups = {}
    current_group = []
    current_start = sorted_data[0].draw_date
    
    for data in sorted_data:
        if (data.draw_date - current_start).days <= period_days:
            current_group.append(data)
        else:
            # 保存当前组
            group_key = f"{current_start.strftime('%Y-%m-%d')} - {current_group[-1].draw_date.strftime('%Y-%m-%d')}"
            groups[group_key] = current_group
            
            # 开始新组
            current_group = [data]
            current_start = data.draw_date
    
    # 保存最后一组
    if current_group:
        group_key = f"{current_start.strftime('%Y-%m-%d')} - {current_group[-1].draw_date.strftime('%Y-%m-%d')}"
        groups[group_key] = current_group
    
    return groups
