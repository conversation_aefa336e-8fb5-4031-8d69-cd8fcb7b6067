# 彩票号码分析系统用户手册

## 目录
1. [系统简介](#系统简介)
2. [安装指南](#安装指南)
3. [快速开始](#快速开始)
4. [功能详解](#功能详解)
5. [数据格式说明](#数据格式说明)
6. [常见问题](#常见问题)
7. [技术支持](#技术支持)

## 系统简介

彩票号码分析系统是一个专业的彩票数据分析工具，支持大乐透、双色球、排列五三种彩票类型的综合分析。系统提供奇偶分析、大小分析、号码遗漏分析、分区比分析等多种分析功能，帮助用户深入了解彩票号码的分布规律。

### 主要特性
- **多彩票类型支持**: 大乐透、双色球、排列五
- **多维度分析**: 奇偶、大小、遗漏、分区比分析
- **友好的图形界面**: 基于tkinter的直观操作界面
- **数据验证**: 自动检测和报告数据问题
- **结果导出**: 支持多种格式的分析结果导出
- **高性能处理**: 支持大数据量的快速分析

### 系统要求
- **操作系统**: Windows 7/10/11, macOS 10.12+, Linux
- **Python版本**: Python 3.8+
- **内存**: 建议4GB以上
- **存储空间**: 100MB以上可用空间

## 安装指南

### 方法一：直接运行（推荐）
1. 确保系统已安装Python 3.8+
2. 下载项目文件到本地目录
3. 打开命令行，进入项目目录
4. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```
5. 运行程序：
   ```bash
   python main.py
   ```

### 方法二：虚拟环境安装
1. 创建虚拟环境：
   ```bash
   python -m venv lottery_env
   ```
2. 激活虚拟环境：
   - Windows: `lottery_env\Scripts\activate`
   - macOS/Linux: `source lottery_env/bin/activate`
3. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```
4. 运行程序：
   ```bash
   python main.py
   ```

### 依赖包说明
- **pandas**: 数据处理和分析
- **numpy**: 数值计算
- **tkinter**: 图形用户界面（Python内置）

## 快速开始

### 第一次使用
1. **启动程序**
   - 双击运行`main.py`或在命令行执行`python main.py`
   - 系统将显示主界面

2. **选择彩票类型**
   - 在界面上方选择要分析的彩票类型（大乐透/双色球/排列五）

3. **导入数据**
   - 点击"导入数据"按钮
   - 选择CSV格式的数据文件
   - 系统将自动验证数据格式并执行所有分析

4. **查看结果**
   - 分析结果将自动显示在下方的结果区域
   - 包含奇偶、大小、遗漏、分区比四种完整分析
   - 可以导出结果到文件

5. **手动分析（可选）**
   - 如需特定分析项目，可点击"手动分析"按钮
   - 在弹出对话框中选择需要的分析类型

### 示例操作流程
```
启动程序 → 选择大乐透 → 导入数据文件 → 自动完成所有分析 → 查看结果 → 导出报告
```

## 功能详解

### 界面布局
主界面包含以下区域：
- **菜单栏**: 文件操作和帮助菜单
- **工具栏**: 常用操作快捷按钮
- **彩票类型选择区**: 选择要分析的彩票类型
- **分析选项区**: 选择分析功能
- **操作按钮区**: 开始分析、清除结果等操作
- **结果显示区**: 显示分析结果

### 数据导入功能
1. **文件选择**
   - 支持CSV格式文件
   - 自动检测文件编码
   - 提供数据预览功能

2. **数据验证**
   - 自动验证数据格式
   - 检查数据完整性
   - 报告数据问题

3. **数据统计**
   - 显示数据量统计
   - 显示日期范围
   - 显示数据质量报告

### 分析功能详解

#### 1. 奇偶分析
- **功能**: 分析号码的奇偶分布规律
- **适用**: 所有彩票类型
- **结果**: 奇偶模式统计、出现频率、间隔分析

#### 2. 大小分析
- **功能**: 分析号码的大小分布规律
- **分界点**: 
  - 大乐透前区: >18为大号
  - 大乐透后区: >6为大号
  - 双色球红球: >16为大号
  - 双色球蓝球: >8为大号
  - 排列五: ≥5为大号
- **结果**: 大小模式统计、出现频率、间隔分析

#### 3. 号码遗漏分析
- **功能**: 分析各号码的遗漏期数
- **统计内容**: 
  - 当前遗漏期数
  - 历史最大遗漏
  - 平均遗漏期数
- **结果**: 遗漏排行榜、遗漏分布图

#### 4. 分区比分析
- **功能**: 分析号码在不同区间的分布比例
- **分区定义**:
  - 大乐透前区: 1-12, 13-24, 25-35 (三区)
  - 双色球红球: 1-11, 12-22, 23-33 (三区)
  - 排列五: 0-4, 5-9 (两区)
- **结果**: 分区比模式统计、出现频率

### 结果导出功能
1. **导出格式**
   - 文本文件(.txt)
   - CSV文件(.csv)

2. **导出内容**
   - 完整分析报告
   - 统计数据
   - 图表数据

3. **导出选项**
   - 包含/排除标题
   - 包含/排除摘要
   - 自定义文件名

## 数据格式说明

### 大乐透数据格式
CSV文件应包含以下列：
```
期号,开奖日期,红球1,红球2,红球3,红球4,红球5,蓝球1,蓝球2
24001,2024-01-01,1,5,15,25,35,2,8
24002,2024-01-03,3,7,17,27,33,1,9
```

### 双色球数据格式
CSV文件应包含以下列：
```
期号,开奖日期,红球1,红球2,红球3,红球4,红球5,红球6,蓝球1
24001,2024-01-02,1,5,15,25,30,33,8
24002,2024-01-04,3,7,17,27,31,32,2
```

### 排列五数据格式
CSV文件应包含以下列：
```
date,period,numbers,sum_value
2024-01-01,24001,1 2 3 4 5,15
2024-01-02,24002,6 7 8 9 0,30
```

### 数据要求
1. **文件编码**: UTF-8
2. **日期格式**: YYYY-MM-DD
3. **号码范围**: 
   - 大乐透前区: 1-35, 后区: 1-12
   - 双色球红球: 1-33, 蓝球: 1-16
   - 排列五: 0-9
4. **数据完整性**: 不允许缺失值

## 常见问题

### Q1: 程序无法启动怎么办？
**A**: 请检查：
1. Python版本是否为3.8+
2. 是否正确安装了依赖包
3. 是否在正确的目录下运行

### Q2: 数据导入失败怎么办？
**A**: 请检查：
1. 文件格式是否为CSV
2. 列名是否正确
3. 数据是否完整
4. 文件编码是否为UTF-8

### Q3: 分析结果不准确怎么办？
**A**: 请确认：
1. 数据来源是否可靠
2. 数据量是否足够（建议至少50期）
3. 数据是否按时间顺序排列

### Q4: 程序运行缓慢怎么办？
**A**: 建议：
1. 关闭其他占用内存的程序
2. 减少分析的数据量
3. 只选择必要的分析选项

### Q5: 如何获取数据文件？
**A**: 
1. 从官方彩票网站下载历史数据
2. 使用数据采集工具
3. 联系技术支持获取示例数据

## 技术支持

### 联系方式
- **邮箱**: <EMAIL>
- **QQ群**: 123456789
- **微信群**: 扫描二维码加入

### 更新日志
- **v1.0.0** (2024-01-01): 初始版本发布
- **v1.0.1** (2024-01-15): 修复数据导入问题
- **v1.1.0** (2024-02-01): 新增分区比分析功能

### 开源信息
- **项目地址**: https://github.com/lottery-analysis/system
- **许可证**: MIT License
- **贡献指南**: 参见CONTRIBUTING.md

---

**版权声明**: 本软件仅供学习和研究使用，不构成任何投资建议。彩票具有随机性，请理性购买。
