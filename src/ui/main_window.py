#!/usr/bin/env python3
"""
主窗口界面实现
基于tkinter实现彩票号码分析系统的主界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
import sys
from typing import Dict, List, Optional, Callable

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5


class MainWindow:
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        self.root = tk.Tk()
        self.setup_window()
        self.create_variables()
        self.create_widgets()
        self.setup_layout()
        self.setup_bindings()
        
        # 回调函数
        self.on_import_data: Optional[Callable] = None
        self.on_start_analysis: Optional[Callable] = None
        self.on_export_results: Optional[Callable] = None
        self.on_clear_results: Optional[Callable] = None
    
    def setup_window(self):
        """设置窗口基本属性"""
        self.root.title("彩票号码分析系统")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # 设置窗口图标（如果有的话）
        try:
            # self.root.iconbitmap("icon.ico")
            pass
        except:
            pass
    
    def create_variables(self):
        """创建界面变量"""
        # 彩票类型选择
        self.lottery_type_var = tk.StringVar(value=LOTTERY_TYPE_DLT)
        
        # 数据文件路径
        self.data_file_path = tk.StringVar()
        
        # 分析选项
        self.analysis_options = {
            'odd_even': tk.BooleanVar(value=True),
            'big_small': tk.BooleanVar(value=True),
            'missing': tk.BooleanVar(value=True),
            'zone_ratio': tk.BooleanVar(value=True)
        }
    
    def create_widgets(self):
        """创建界面控件"""
        self.create_menu()
        self.create_toolbar()
        self.create_lottery_type_frame()
        self.create_analysis_options_frame()
        self.create_analysis_button_frame()
        self.create_results_frame()
    
    def create_menu(self):
        """创建菜单栏"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # 文件菜单
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="文件", menu=file_menu)
        file_menu.add_command(label="导入数据", command=self.import_data)
        file_menu.add_command(label="导出结果", command=self.export_results)
        file_menu.add_separator()
        file_menu.add_command(label="退出", command=self.root.quit)
        
        # 帮助菜单
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="帮助", menu=help_menu)
        help_menu.add_command(label="使用说明", command=self.show_help)
        help_menu.add_command(label="关于", command=self.show_about)
    
    def create_toolbar(self):
        """创建工具栏"""
        self.toolbar_frame = ttk.Frame(self.root)

        # 工具栏按钮
        ttk.Button(
            self.toolbar_frame,
            text="导入数据",
            command=self.import_data
        ).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(
            self.toolbar_frame,
            text="导出结果",
            command=self.export_results
        ).pack(side=tk.LEFT, padx=5, pady=5)

        ttk.Button(
            self.toolbar_frame,
            text="清除结果",
            command=self.clear_results
        ).pack(side=tk.LEFT, padx=5, pady=5)
    
    def create_lottery_type_frame(self):
        """创建彩票类型选择区"""
        self.lottery_frame = ttk.LabelFrame(self.root, text="彩票类型", padding=10)
        
        # 彩票类型单选按钮
        lottery_types = [
            (LOTTERY_TYPE_DLT, "大乐透"),
            (LOTTERY_TYPE_SSQ, "双色球"),
            (LOTTERY_TYPE_PL5, "排列五")
        ]
        
        for value, text in lottery_types:
            ttk.Radiobutton(
                self.lottery_frame,
                text=text,
                variable=self.lottery_type_var,
                value=value,
                command=self.on_lottery_type_changed
            ).pack(side=tk.LEFT, padx=10)
        
        # 数据文件选择
        file_frame = ttk.Frame(self.lottery_frame)
        file_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Label(file_frame, text="数据文件:").pack(side=tk.LEFT)
        
        self.file_entry = ttk.Entry(
            file_frame, 
            textvariable=self.data_file_path,
            state='readonly'
        )
        self.file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(5, 5))
        
        ttk.Button(
            file_frame, 
            text="浏览", 
            command=self.browse_file
        ).pack(side=tk.RIGHT)
    
    def create_analysis_options_frame(self):
        """创建分析选项区"""
        self.options_frame = ttk.LabelFrame(self.root, text="分析选项", padding=10)
        
        # 分析选项复选框
        options = [
            ('odd_even', "奇偶分析"),
            ('big_small', "大小分析"),
            ('missing', "号码遗漏分析"),
            ('zone_ratio', "分区比分析")
        ]
        
        # 创建两行布局
        row1_frame = ttk.Frame(self.options_frame)
        row1_frame.pack(fill=tk.X)
        
        row2_frame = ttk.Frame(self.options_frame)
        row2_frame.pack(fill=tk.X, pady=(5, 0))
        
        for i, (key, text) in enumerate(options):
            parent = row1_frame if i < 2 else row2_frame
            ttk.Checkbutton(
                parent,
                text=text,
                variable=self.analysis_options[key]
            ).pack(side=tk.LEFT, padx=10)

    def create_analysis_button_frame(self):
        """创建分析按钮区域"""
        self.button_frame = ttk.Frame(self.root)

        # 开始分析按钮
        self.start_analysis_btn = ttk.Button(
            self.button_frame,
            text="开始分析",
            command=self.start_analysis
        )
        self.start_analysis_btn.pack(side=tk.LEFT, padx=10, pady=10)

    def create_results_frame(self):
        """创建结果显示区"""
        self.results_frame = ttk.LabelFrame(self.root, text="数据表格", padding=10)

        # 直接创建数据表格，不使用分页
        self.create_data_table(self.results_frame)

    def create_data_table(self, parent):
        """创建数据表格"""
        # 表格框架
        table_frame = ttk.Frame(parent)
        table_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # 定义列
        columns = ('期号', '开奖日期', '前区号码', '后区号码', '奇偶模式', '大小模式', '分区分布')
        self.data_tree = ttk.Treeview(table_frame, columns=columns, show='headings')

        # 设置列标题和宽度
        column_widths = {
            '期号': 80,
            '开奖日期': 100,
            '前区号码': 150,
            '后区号码': 80,
            '奇偶模式': 100,
            '大小模式': 100,
            '分区分布': 120
        }
        for col in columns:
            self.data_tree.heading(col, text=col)
            self.data_tree.column(col, width=column_widths.get(col, 100))

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.data_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.data_tree.xview)
        self.data_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 布局
        self.data_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)



    def setup_layout(self):
        """设置界面布局"""
        self.toolbar_frame.pack(fill=tk.X, padx=5, pady=5)
        self.lottery_frame.pack(fill=tk.X, padx=10, pady=5)
        self.options_frame.pack(fill=tk.X, padx=10, pady=5)
        self.button_frame.pack(fill=tk.X, padx=10, pady=5)
        self.results_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
    
    def setup_bindings(self):
        """设置事件绑定"""
        # 窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def on_lottery_type_changed(self):
        """彩票类型改变事件"""
        # 清空当前数据文件路径
        self.data_file_path.set("")
        # 可以在这里添加其他类型相关的更新逻辑
    
    def browse_file(self):
        """浏览文件"""
        file_path = filedialog.askopenfilename(
            title="选择数据文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")]
        )
        if file_path:
            self.data_file_path.set(file_path)
    
    def import_data(self):
        """导入数据"""
        if self.on_import_data:
            self.on_import_data()
        else:
            self.browse_file()
    
    def start_analysis(self):
        """手动分析（可选择特定分析项目）"""
        if self.on_start_analysis:
            self.on_start_analysis()
        else:
            messagebox.showinfo("提示", "手动分析功能：可选择特定分析项目进行分析")
    
    def export_results(self):
        """导出结果"""
        if self.on_export_results:
            self.on_export_results()
        else:
            messagebox.showinfo("提示", "导出功能尚未实现")
    
    def clear_results(self):
        """清除结果"""
        if self.on_clear_results:
            self.on_clear_results()
        else:
            # 清除数据表格
            self.clear_data_table()
    
    def show_help(self):
        """显示帮助"""
        help_text = """
彩票号码分析系统使用说明：

1. 选择彩票类型（大乐透、双色球、排列五）
2. 点击"导入数据"按钮选择数据文件（CSV格式）
3. 系统将自动执行所有分析并显示结果
4. 可选择"手动分析"进行特定项目分析
5. 查看分析结果
6. 可选择导出结果到文件

注意事项：
- 数据文件必须是CSV格式
- 确保数据格式正确
- 分析过程可能需要一些时间
        """
        messagebox.showinfo("使用说明", help_text)
    
    def show_about(self):
        """显示关于信息"""
        about_text = """
彩票号码分析系统 v1.0

一个专业的彩票号码分析工具，支持：
- 大乐透分析
- 双色球分析  
- 排列五分析

主要功能：
- 奇偶分析
- 大小分析
- 号码遗漏分析
- 分区比分析

开发语言：Python
界面框架：tkinter
        """
        messagebox.showinfo("关于", about_text)
    
    def on_closing(self):
        """窗口关闭事件"""
        self.root.quit()
        self.root.destroy()
    
    def run(self):
        """运行主窗口"""
        self.root.mainloop()
    
    # 公共接口方法
    def get_lottery_type(self) -> str:
        """获取选中的彩票类型"""
        return self.lottery_type_var.get()
    
    def get_data_file_path(self) -> str:
        """获取数据文件路径"""
        return self.data_file_path.get()
    
    def get_analysis_options(self) -> Dict[str, bool]:
        """获取分析选项"""
        return {key: var.get() for key, var in self.analysis_options.items()}
    
    def set_results_text(self, text: str):
        """设置结果文本（已移除分析结果页面，此方法保留以兼容现有代码）"""
        # 不再显示分析结果文本，只保留数据表格
        pass

    def append_results_text(self, text: str):
        """追加结果文本（已移除分析结果页面，此方法保留以兼容现有代码）"""
        # 不再显示分析结果文本，只保留数据表格
        pass
    
    def show_message(self, title: str, message: str, msg_type: str = "info"):
        """显示消息"""
        if msg_type == "info":
            messagebox.showinfo(title, message)
        elif msg_type == "warning":
            messagebox.showwarning(title, message)
        elif msg_type == "error":
            messagebox.showerror(title, message)

    def populate_data_table(self, lottery_data_list: List):
        """填充数据表格"""
        # 清空现有数据
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)

        # 添加数据
        for data in lottery_data_list:
            # 格式化前区号码
            front_numbers = " ".join(f"{num:02d}" for num in data.get_front_numbers())

            # 格式化后区号码
            back_numbers = " ".join(f"{num:02d}" for num in data.get_back_numbers())

            # 格式化日期
            date_str = data.draw_date.strftime('%Y-%m-%d') if data.draw_date else ''

            # 计算分析信息
            odd_even_pattern = self._calculate_odd_even_pattern(data)
            big_small_pattern = self._calculate_big_small_pattern(data)
            zone_distribution = self._calculate_zone_distribution(data)

            # 插入行
            self.data_tree.insert('', tk.END, values=(
                data.issue_number,
                date_str,
                front_numbers,
                back_numbers,
                odd_even_pattern,
                big_small_pattern,
                zone_distribution
            ))

    def set_data_table(self, lottery_data_list: List):
        """设置数据表格内容"""
        self.populate_data_table(lottery_data_list)

    def clear_data_table(self):
        """清空数据表格"""
        for item in self.data_tree.get_children():
            self.data_tree.delete(item)

    def _calculate_odd_even_pattern(self, data):
        """计算奇偶模式"""
        try:
            front_numbers = data.get_front_numbers()
            if front_numbers:
                # 直接计算奇偶模式
                return ''.join('1' if num % 2 == 1 else '0' for num in front_numbers)
            return ""
        except Exception as e:
            return ""

    def _calculate_big_small_pattern(self, data):
        """计算大小模式"""
        try:
            front_numbers = data.get_front_numbers()
            if front_numbers:
                # 根据彩票类型确定大小分界
                if data.lottery_type == 'dlt':
                    boundary = 18  # 大乐透前区大小分界
                elif data.lottery_type == 'ssq':
                    boundary = 17  # 双色球红球大小分界
                elif data.lottery_type == 'pl5':
                    boundary = 5   # 排列五大小分界
                else:
                    boundary = 18

                # 计算大小模式
                if data.lottery_type == 'pl5':
                    return ''.join('1' if num >= boundary else '0' for num in front_numbers)
                else:
                    return ''.join('1' if num > boundary else '0' for num in front_numbers)
            return ""
        except Exception as e:
            return ""

    def _calculate_zone_distribution(self, data):
        """计算分区分布"""
        try:
            front_numbers = data.get_front_numbers()
            if not front_numbers:
                return ""

            # 根据彩票类型计算分区分布
            if data.lottery_type == 'dlt':
                # 大乐透7个区，每区5个号码
                zones = [0] * 7
                for num in front_numbers:
                    zone_index = (num - 1) // 5
                    if 0 <= zone_index < 7:
                        zones[zone_index] += 1
                return ":".join(str(count) for count in zones)

            elif data.lottery_type == 'ssq':
                # 双色球7个区
                zones = [0] * 7
                for num in front_numbers:
                    if 1 <= num <= 5:
                        zones[0] += 1
                    elif 6 <= num <= 10:
                        zones[1] += 1
                    elif 11 <= num <= 15:
                        zones[2] += 1
                    elif 16 <= num <= 20:
                        zones[3] += 1
                    elif 21 <= num <= 25:
                        zones[4] += 1
                    elif 26 <= num <= 30:
                        zones[5] += 1
                    elif 31 <= num <= 33:
                        zones[6] += 1
                return ":".join(str(count) for count in zones)

            elif data.lottery_type == 'pl5':
                # 排列五2个区
                small_count = sum(1 for num in front_numbers if num < 5)
                big_count = sum(1 for num in front_numbers if num >= 5)
                return f"{small_count}:{big_count}"

            return ""
        except Exception as e:
            return ""


if __name__ == "__main__":
    # 测试主窗口 - 添加简单的导入功能
    app = MainWindow()

    # 添加简单的导入数据功能
    def simple_import_data():
        """简单的导入数据功能（用于测试）"""
        from tkinter import filedialog, messagebox

        # 选择文件
        file_path = filedialog.askopenfilename(
            title="选择大乐透数据文件",
            filetypes=[("CSV文件", "*.csv"), ("所有文件", "*.*")],
            initialdir="data"
        )

        if not file_path:
            return

        try:
            # 设置文件路径
            app.data_file_path.set(file_path)

            # 导入数据读取器和验证器
            import sys
            import os
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

            from src.data.reader import DataReader
            from src.data.validator import DataValidator
            from src.data.config import LOTTERY_TYPE_DLT

            # 读取数据
            reader = DataReader()
            data_list = reader.read_csv(file_path, LOTTERY_TYPE_DLT)

            # 验证数据
            validator = DataValidator()
            validation_result = validator.validate_data_list(data_list)

            # 显示在表格中
            if hasattr(app, 'set_data_table'):
                app.set_data_table(data_list)

            # 显示验证结果
            summary = f"""数据导入成功！

文件: {os.path.basename(file_path)}
总数据: {validation_result['total_count']} 条
有效数据: {validation_result['valid_count']} 条
无效数据: {validation_result['invalid_count']} 条
验证通过率: {validation_result['valid_count']/validation_result['total_count']*100:.1f}%

数据已显示在数据表格中。"""

            app.set_results_text(summary)
            messagebox.showinfo("导入成功", f"成功导入 {validation_result['valid_count']} 条有效数据")

        except Exception as e:
            error_msg = f"导入数据失败：{str(e)}"
            app.set_results_text(error_msg)
            messagebox.showerror("导入失败", error_msg)

    # 绑定导入功能
    app.on_import_data = simple_import_data

    print("🚀 主窗口测试模式")
    print("💡 现在可以点击'导入数据'按钮测试功能")
    print("📁 建议选择 data/dlt_data.csv 文件")

    app.run()
