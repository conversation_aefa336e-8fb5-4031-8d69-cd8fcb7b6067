#!/usr/bin/env python3
"""
排列五分析器测试模块
测试 PL5Analyzer 类的各种分析功能
"""

import pytest
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.analysis.pl5_analyzer import PL5Analyzer
from src.data.models import LotteryData
from src.data.config import LOTTERY_TYPE_PL5


class TestPL5Analyzer:
    """排列五分析器基础功能测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = PL5Analyzer()
        
        # 创建测试数据
        self.test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 2, 3, 4, 5], LOTTERY_TYPE_PL5),
            LotteryData("24002", datetime(2024, 1, 2), [6, 7, 8, 9, 0], LOTTERY_TYPE_PL5),
            LotteryData("24003", datetime(2024, 1, 3), [1, 3, 5, 7, 9], LOTTERY_TYPE_PL5),
            LotteryData("24004", datetime(2024, 1, 4), [2, 4, 6, 8, 0], LOTTERY_TYPE_PL5),
            LotteryData("24005", datetime(2024, 1, 5), [9, 8, 7, 6, 5], LOTTERY_TYPE_PL5)
        ]
        
        self.analyzer.load_data(self.test_data)
    
    def test_analyze_odd_even_distribution(self):
        """测试奇偶分析"""
        result = self.analyzer.analyze_odd_even_distribution()
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '整体'
        assert result['total_periods'] == 5
        
        # 验证详细结果
        for detail in result['recent_details']:
            assert len(detail['pattern']) == 5  # 5位数字
            assert detail['odd_count'] + detail['even_count'] == 5
    
    def test_analyze_big_small_distribution(self):
        """测试大小分析"""
        result = self.analyzer.analyze_big_small_distribution()
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '整体'
        assert result['total_periods'] == 5
        
        # 验证详细结果
        for detail in result['recent_details']:
            assert len(detail['pattern']) == 5  # 5位数字
            assert detail['big_count'] + detail['small_count'] == 5
    
    def test_analyze_zone_ratio(self):
        """测试分区比分析"""
        result = self.analyzer.analyze_zone_ratio()
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '整体'
        assert result['total_periods'] == 5
        
        # 验证分区定义（排列五只有2个分区）
        assert len(result['zone_definitions']) == 2
        
        # 验证按位分区统计
        assert 'position_zone_statistics' in result
        assert len(result['position_zone_statistics']) == 5  # 5个位置
        
        for pos_stat in result['position_zone_statistics']:
            assert 'position' in pos_stat
            assert 'zone1_count' in pos_stat
            assert 'zone2_count' in pos_stat
            assert 'zone1_rate' in pos_stat
            assert 'zone2_rate' in pos_stat
            assert 1 <= pos_stat['position'] <= 5
    
    def test_calculate_number_intervals(self):
        """测试号码遗漏分析"""
        result = self.analyzer.calculate_number_intervals(7)
        
        assert result['number'] == 7
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '整体'
        assert result['total_periods'] == 5
        
        # 验证出现记录包含位置信息
        for occurrence in result['occurrences']:
            assert 'positions_in_number' in occurrence
            assert isinstance(occurrence['positions_in_number'], list)
    
    def test_calculate_all_numbers_missing(self):
        """测试所有号码遗漏统计"""
        result = self.analyzer.calculate_all_numbers_missing()
        
        assert result['zone_type'] == 'front'
        assert result['zone_name'] == '整体'
        assert result['total_numbers'] == 10  # 0-9
        assert result['total_periods'] == 5
        
        # 验证号码统计
        assert len(result['all_numbers_stats']) == 10
        for stat in result['all_numbers_stats']:
            assert 0 <= stat['number'] <= 9
    
    def test_calculate_position_number_intervals(self):
        """测试按位号码遗漏分析"""
        result = self.analyzer.calculate_position_number_intervals(3, 1)
        
        assert result['number'] == 3
        assert result['position'] == 1
        assert result['total_periods'] == 5
        
        # 验证出现记录包含目标位置信息
        for occurrence in result['occurrences']:
            assert 'target_position' in occurrence
            assert occurrence['target_position'] == 1
    
    def test_analyze_position_odd_even(self):
        """测试按位奇偶分析"""
        result = self.analyzer.analyze_position_odd_even(1)

        assert result['position'] == 1
        assert result['total_periods'] == 5
        assert 'pattern_statistics' in result
        assert 'odd_count' in result
        assert 'even_count' in result
        assert 'odd_rate' in result
        assert 'even_rate' in result

        # 验证数据一致性
        assert result['odd_count'] + result['even_count'] == result['total_periods']
        assert abs(result['odd_rate'] + result['even_rate'] - 1.0) < 0.001
    
    def test_analyze_position_big_small(self):
        """测试按位大小分析"""
        result = self.analyzer.analyze_position_big_small(1)

        assert result['position'] == 1
        assert result['total_periods'] == 5
        assert 'pattern_statistics' in result
        assert 'big_count' in result
        assert 'small_count' in result
        assert 'big_rate' in result
        assert 'small_rate' in result

        # 验证数据一致性
        assert result['big_count'] + result['small_count'] == result['total_periods']
        assert abs(result['big_rate'] + result['small_rate'] - 1.0) < 0.001


class TestPL5AnalyzerEdgeCases:
    """排列五分析器边界条件测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = PL5Analyzer()
    
    def test_invalid_number_range(self):
        """测试无效号码范围"""
        test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 2, 3, 4, 5], LOTTERY_TYPE_PL5)
        ]
        self.analyzer.load_data(test_data)
        
        with pytest.raises(ValueError, match="排列五号码必须在0-9之间"):
            self.analyzer.calculate_number_intervals(-1)
        
        with pytest.raises(ValueError, match="排列五号码必须在0-9之间"):
            self.analyzer.calculate_number_intervals(10)
    
    def test_invalid_position_range(self):
        """测试无效位置范围"""
        test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 2, 3, 4, 5], LOTTERY_TYPE_PL5)
        ]
        self.analyzer.load_data(test_data)
        
        with pytest.raises(ValueError, match="位置必须在1-5之间"):
            self.analyzer.calculate_position_number_intervals(3, 0)
        
        with pytest.raises(ValueError, match="位置必须在1-5之间"):
            self.analyzer.calculate_position_number_intervals(3, 6)
        
        with pytest.raises(ValueError, match="位置必须在1-5之间"):
            self.analyzer.analyze_position_odd_even(0)
        
        with pytest.raises(ValueError, match="位置必须在1-5之间"):
            self.analyzer.analyze_position_big_small(6)
    
    def test_incomplete_numbers(self):
        """测试不完整的号码数据"""
        test_data = [
            LotteryData("24001", datetime(2024, 1, 1), [1, 2, 3], LOTTERY_TYPE_PL5)  # 只有3位
        ]
        self.analyzer.load_data(test_data)
        
        # 应该能处理不完整的数据，但可能跳过这些记录
        result = self.analyzer.analyze_odd_even_distribution()
        # 具体行为取决于实现，这里主要确保不会崩溃
        assert isinstance(result, dict)


class TestPL5AnalyzerIntegration:
    """排列五分析器集成测试"""
    
    def setup_method(self):
        """测试前准备"""
        self.analyzer = PL5Analyzer()
        
        # 创建更多测试数据
        self.test_data = []
        for i in range(10):
            numbers = [(i+j) % 10 for j in range(5)]
            self.test_data.append(
                LotteryData(
                    f"24{i+1:03d}",
                    datetime(2024, 1, i+1),
                    numbers,
                    LOTTERY_TYPE_PL5
                )
            )
        
        self.analyzer.load_data(self.test_data)
    
    def test_comprehensive_analysis(self):
        """测试综合分析功能"""
        # 测试整体分析
        analyses = [
            'analyze_odd_even_distribution',
            'analyze_big_small_distribution',
            'analyze_zone_ratio'
        ]
        
        for method_name in analyses:
            method = getattr(self.analyzer, method_name)
            result = method()
            
            assert result['total_periods'] == 10
            assert len(result['recent_details']) <= 20  # 最多20期
            assert result['latest_pattern'] != ""
    
    def test_position_analysis_consistency(self):
        """测试按位分析的一致性"""
        # 测试所有位置的奇偶分析
        for position in range(1, 6):
            result = self.analyzer.analyze_position_odd_even(position)
            assert result['position'] == position
            assert result['total_periods'] == 10
            
            # 测试按位大小分析
            big_small_result = self.analyzer.analyze_position_big_small(position)
            assert big_small_result['position'] == position
            assert big_small_result['total_periods'] == 10
    
    def test_number_position_analysis(self):
        """测试号码按位分析"""
        # 测试所有数字在所有位置的遗漏分析
        for number in range(10):
            for position in range(1, 6):
                result = self.analyzer.calculate_position_number_intervals(number, position)
                assert result['number'] == number
                assert result['position'] == position
                assert result['total_periods'] == 10


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
