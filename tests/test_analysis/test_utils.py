#!/usr/bin/env python3
"""
分析工具函数测试模块
测试 analysis.utils 模块的各种工具函数
"""

import pytest
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.analysis.utils import (
    generate_odd_even_pattern, count_odd_even,
    generate_big_small_pattern, count_big_small,
    generate_zone_ratio_pattern, count_zone_distribution,
    calculate_statistics, format_date_range
)
from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5


class TestOddEvenFunctions:
    """奇偶分析函数测试"""
    
    def test_generate_odd_even_pattern_dlt_front(self):
        """测试大乐透前区奇偶模式生成"""
        numbers = [1, 5, 15, 25, 35]  # 全奇数
        pattern = generate_odd_even_pattern(numbers)
        assert pattern == "11111"

        numbers = [2, 8, 18, 28, 34]  # 全偶数
        pattern = generate_odd_even_pattern(numbers)
        assert pattern == "00000"

        numbers = [1, 2, 15, 28, 35]  # 混合
        pattern = generate_odd_even_pattern(numbers)
        assert pattern == "10101"
    
    def test_generate_odd_even_pattern_dlt_back(self):
        """测试大乐透后区奇偶模式生成"""
        numbers = [1, 3]  # 全奇数
        pattern = generate_odd_even_pattern(numbers)
        assert pattern == "11"

        numbers = [2, 8]  # 全偶数
        pattern = generate_odd_even_pattern(numbers)
        assert pattern == "00"
        
        numbers = [1, 8]  # 混合
        pattern = generate_odd_even_pattern(numbers)
        assert pattern == "10"

    def test_generate_odd_even_pattern_ssq(self):
        """测试双色球奇偶模式生成"""
        # 红球区
        numbers = [1, 5, 15, 25, 30, 33]  # 5奇1偶
        pattern = generate_odd_even_pattern(numbers)
        assert pattern == "111101"

        # 蓝球区
        numbers = [7]  # 奇数
        pattern = generate_odd_even_pattern(numbers)
        assert pattern == "1"

        numbers = [8]  # 偶数
        pattern = generate_odd_even_pattern(numbers)
        assert pattern == "0"

    def test_generate_odd_even_pattern_pl5(self):
        """测试排列五奇偶模式生成"""
        numbers = [1, 2, 3, 4, 5]
        pattern = generate_odd_even_pattern(numbers)
        assert pattern == "10101"

        numbers = [0, 1, 2, 3, 4]
        pattern = generate_odd_even_pattern(numbers)
        assert pattern == "01010"
    
    def test_count_odd_even_dlt(self):
        """测试大乐透奇偶数量统计"""
        numbers = [1, 5, 15, 25, 35]  # 全奇数
        odd_count, even_count = count_odd_even(numbers)
        assert odd_count == 5
        assert even_count == 0

        numbers = [1, 2, 15, 28, 35]  # 3奇2偶
        odd_count, even_count = count_odd_even(numbers)
        assert odd_count == 3
        assert even_count == 2

    def test_count_odd_even_empty(self):
        """测试空号码列表的奇偶统计"""
        odd_count, even_count = count_odd_even([])
        assert odd_count == 0
        assert even_count == 0


class TestBigSmallFunctions:
    """大小分析函数测试"""
    
    def test_generate_big_small_pattern_dlt_front(self):
        """测试大乐透前区大小模式生成"""
        numbers = [1, 5, 15, 25, 35]  # 1,5,15小号，25,35大号
        pattern = generate_big_small_pattern(numbers, LOTTERY_TYPE_DLT, 'front')
        assert pattern == "00011"
        
        numbers = [19, 20, 21, 22, 23]  # 全大号
        pattern = generate_big_small_pattern(numbers, LOTTERY_TYPE_DLT, 'front')
        assert pattern == "11111"
    
    def test_generate_big_small_pattern_dlt_back(self):
        """测试大乐透后区大小模式生成"""
        numbers = [1, 3]  # 全小号
        pattern = generate_big_small_pattern(numbers, LOTTERY_TYPE_DLT, 'back')
        assert pattern == "00"
        
        numbers = [8, 12]  # 全大号
        pattern = generate_big_small_pattern(numbers, LOTTERY_TYPE_DLT, 'back')
        assert pattern == "11"
    
    def test_generate_big_small_pattern_ssq(self):
        """测试双色球大小模式生成"""
        # 红球区（分界点17）
        numbers = [1, 5, 15, 25, 30, 33]  # 前3个小号，后3个大号
        pattern = generate_big_small_pattern(numbers, LOTTERY_TYPE_SSQ, 'front')
        assert pattern == "000111"
        
        # 蓝球区（分界点9）
        numbers = [5]  # 小号
        pattern = generate_big_small_pattern(numbers, LOTTERY_TYPE_SSQ, 'back')
        assert pattern == "0"
        
        numbers = [12]  # 大号
        pattern = generate_big_small_pattern(numbers, LOTTERY_TYPE_SSQ, 'back')
        assert pattern == "1"
    
    def test_generate_big_small_pattern_pl5(self):
        """测试排列五大小模式生成"""
        numbers = [1, 2, 3, 4, 5]  # 前4个小号，最后1个大号
        pattern = generate_big_small_pattern(numbers, LOTTERY_TYPE_PL5)
        assert pattern == "00001"
        
        numbers = [5, 6, 7, 8, 9]  # 全大号
        pattern = generate_big_small_pattern(numbers, LOTTERY_TYPE_PL5)
        assert pattern == "11111"
    
    def test_count_big_small_dlt(self):
        """测试大乐透大小数量统计"""
        numbers = [1, 5, 15, 25, 35]  # 3小2大
        big_count, small_count = count_big_small(numbers, LOTTERY_TYPE_DLT, 'front')
        assert big_count == 2
        assert small_count == 3
    
    def test_count_big_small_empty(self):
        """测试空号码列表的大小统计"""
        big_count, small_count = count_big_small([], LOTTERY_TYPE_DLT, 'front')
        assert big_count == 0
        assert small_count == 0


class TestZoneRatioFunctions:
    """分区比分析函数测试"""
    
    def test_generate_zone_ratio_pattern_dlt_front(self):
        """测试大乐透前区分区比模式生成"""
        numbers = [1, 8, 15, 22, 29]  # 每个分区1个号码
        pattern = generate_zone_ratio_pattern(numbers, LOTTERY_TYPE_DLT, 'front')
        # 应该是"1:1:1:1:1:0:0"格式
        assert ":" in pattern
        parts = pattern.split(":")
        assert len(parts) == 7  # 7个分区
        assert sum(int(p) for p in parts) == 5  # 总共5个号码
    
    def test_generate_zone_ratio_pattern_dlt_back(self):
        """测试大乐透后区分区比模式生成"""
        numbers = [3, 9]  # 第1区1个，第2区1个
        pattern = generate_zone_ratio_pattern(numbers, LOTTERY_TYPE_DLT, 'back')
        assert pattern == "1:1"
    
    def test_generate_zone_ratio_pattern_ssq(self):
        """测试双色球分区比模式生成"""
        # 红球区
        numbers = [1, 8, 15, 22, 28, 33]  # 分布在不同分区
        pattern = generate_zone_ratio_pattern(numbers, LOTTERY_TYPE_SSQ, 'front')
        parts = pattern.split(":")
        assert len(parts) == 7  # 7个分区
        assert sum(int(p) for p in parts) == 6  # 总共6个号码
        
        # 蓝球区
        numbers = [5]  # 第1区
        pattern = generate_zone_ratio_pattern(numbers, LOTTERY_TYPE_SSQ, 'back')
        assert pattern == "1:0"
    
    def test_generate_zone_ratio_pattern_pl5(self):
        """测试排列五分区比模式生成"""
        numbers = [1, 2, 7, 8, 9]  # 2个小号，3个大号
        pattern = generate_zone_ratio_pattern(numbers, LOTTERY_TYPE_PL5)
        assert pattern == "2:3"
    
    def test_count_zone_distribution_dlt(self):
        """测试大乐透分区分布统计"""
        numbers = [1, 8, 15, 22, 29]
        distribution = count_zone_distribution(numbers, LOTTERY_TYPE_DLT, 'front')
        
        assert isinstance(distribution, dict)
        # 每个分区应该有1个号码
        total_count = sum(distribution.values())
        assert total_count == 5
    
    def test_count_zone_distribution_empty(self):
        """测试空号码列表的分区分布"""
        distribution = count_zone_distribution([], LOTTERY_TYPE_DLT, 'front')
        assert isinstance(distribution, dict)
        assert sum(distribution.values()) == 0


class TestStatisticsFunctions:
    """统计函数测试"""
    
    def test_calculate_statistics_normal(self):
        """测试正常数据的统计计算"""
        data = [1, 2, 3, 4, 5]
        stats = calculate_statistics(data)
        
        assert stats['count'] == 5
        assert stats['sum'] == 15
        assert stats['mean'] == 3.0
        assert stats['min'] == 1
        assert stats['max'] == 5
        assert 'std' in stats
        assert 'median' in stats
    
    def test_calculate_statistics_empty(self):
        """测试空数据的统计计算"""
        stats = calculate_statistics([])
        
        assert stats['count'] == 0
        assert stats['sum'] == 0
        assert stats['mean'] == 0
        assert stats['min'] == 0
        assert stats['max'] == 0
        assert stats['std'] == 0
        assert stats['median'] == 0
    
    def test_calculate_statistics_single_value(self):
        """测试单个值的统计计算"""
        stats = calculate_statistics([42])
        
        assert stats['count'] == 1
        assert stats['sum'] == 42
        assert stats['mean'] == 42
        assert stats['min'] == 42
        assert stats['max'] == 42
        assert stats['std'] == 0
        assert stats['median'] == 42
    
    def test_format_date_range(self):
        """测试日期范围格式化"""
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)
        
        formatted = format_date_range(start_date, end_date)
        assert "2024-01-01" in formatted
        assert "2024-01-31" in formatted
        assert "至" in formatted or "到" in formatted or "-" in formatted


class TestEdgeCases:
    """边界条件测试"""
    
    def test_invalid_lottery_type(self):
        """测试无效彩票类型"""
        numbers = [1, 2, 3, 4, 5]

        # generate_odd_even_pattern 不需要彩票类型参数，跳过这个测试

        with pytest.raises(ValueError):
            generate_big_small_pattern(numbers, "invalid_type")

        with pytest.raises(ValueError):
            generate_zone_ratio_pattern(numbers, "invalid_type")
    
    def test_invalid_zone_type(self):
        """测试无效区域类型"""
        numbers = [1, 2, 3, 4, 5]
        
        # generate_odd_even_pattern 不需要 zone_type 参数，跳过这个测试
        pass
        
        with pytest.raises(ValueError):
            generate_big_small_pattern(numbers, LOTTERY_TYPE_DLT, "invalid_zone")
    
    def test_out_of_range_numbers(self):
        """测试超出范围的号码"""
        # 大乐透前区号码超出范围
        numbers = [1, 2, 3, 4, 36]  # 36超出范围
        # 应该能处理，但可能产生意外结果
        pattern = generate_odd_even_pattern(numbers)
        assert len(pattern) == 5

    def test_duplicate_numbers(self):
        """测试重复号码"""
        numbers = [1, 1, 2, 2, 3]
        pattern = generate_odd_even_pattern(numbers)
        assert len(pattern) == 5
        assert pattern == "11001"

    def test_unsorted_numbers(self):
        """测试未排序的号码"""
        numbers = [5, 1, 3, 2, 4]
        pattern = generate_odd_even_pattern(numbers)
        assert len(pattern) == 5
        # 应该按输入顺序处理: 5(奇)1(奇)3(奇)2(偶)4(偶)
        assert pattern == "11100"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
