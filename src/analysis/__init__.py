"""
分析模块

包含基础分析类和各种彩票类型的专用分析器。
提供奇偶分析、大小分析、号码遗漏分析、分区比分析等功能。
"""

from .base import LotteryAnalyzer
from .dlt_analyzer import DLTAnalyzer
from .ssq_analyzer import SSQAnalyzer
from .pl5_analyzer import PL5Analyzer
from .utils import (
    is_odd, is_big,
    generate_odd_even_pattern, generate_big_small_pattern, generate_zone_ratio_pattern,
    count_odd_even, count_big_small, count_zone_distribution,
    get_zone_for_number, calculate_statistics, find_consecutive_patterns,
    get_frequency_ranking, format_date_range, calculate_days_between,
    group_data_by_period
)

__all__ = [
    'LotteryAnalyzer',
    'DLTAnalyzer', 'SSQAnalyzer', 'PL5Analyzer',
    'is_odd', 'is_big',
    'generate_odd_even_pattern', 'generate_big_small_pattern', 'generate_zone_ratio_pattern',
    'count_odd_even', 'count_big_small', 'count_zone_distribution',
    'get_zone_for_number', 'calculate_statistics', 'find_consecutive_patterns',
    'get_frequency_ranking', 'format_date_range', 'calculate_days_between',
    'group_data_by_period'
]
