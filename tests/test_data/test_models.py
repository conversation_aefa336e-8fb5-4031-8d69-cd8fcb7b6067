#!/usr/bin/env python3
"""
数据模型测试模块
测试 LotteryData 类的各种功能
"""

import pytest
import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..'))

from src.data.models import LotteryData
from src.data.config import LOTTERY_TYPE_DLT, LOTTERY_TYPE_SSQ, LOTTERY_TYPE_PL5


class TestLotteryData:
    """LotteryData 类测试"""

    def test_dlt_creation_valid(self):
        """测试大乐透数据创建 - 正常情况"""
        data = LotteryData(
            issue_number="24001",
            draw_date=datetime(2024, 1, 1),
            lottery_type=LOTTERY_TYPE_DLT,
            numbers=[1, 5, 15, 25, 35, 2, 8]
        )

        assert data.issue_number == "24001"
        assert data.draw_date == datetime(2024, 1, 1)
        assert data.lottery_type == LOTTERY_TYPE_DLT
        assert data.get_front_numbers() == [1, 5, 15, 25, 35]
        assert data.get_back_numbers() == [2, 8]

    def test_ssq_creation_valid(self):
        """测试双色球数据创建 - 正常情况"""
        data = LotteryData(
            issue_number="24001",
            draw_date=datetime(2024, 1, 2),
            lottery_type=LOTTERY_TYPE_SSQ,
            numbers=[1, 5, 15, 25, 30, 33, 8]
        )

        assert data.issue_number == "24001"
        assert data.draw_date == datetime(2024, 1, 2)
        assert data.lottery_type == LOTTERY_TYPE_SSQ
        assert data.get_front_numbers() == [1, 5, 15, 25, 30, 33]
        assert data.get_back_numbers() == [8]

    def test_pl5_creation_valid(self):
        """测试排列五数据创建 - 正常情况"""
        data = LotteryData(
            issue_number="24001",
            draw_date=datetime(2024, 1, 1),
            lottery_type=LOTTERY_TYPE_PL5,
            numbers=[1, 2, 3, 4, 5]
        )

        assert data.issue_number == "24001"
        assert data.draw_date == datetime(2024, 1, 1)
        assert data.lottery_type == LOTTERY_TYPE_PL5
        assert data.get_front_numbers() == [1, 2, 3, 4, 5]
        assert data.get_back_numbers() == []

    def test_str_representation(self):
        """测试字符串表示"""
        data = LotteryData(
            issue_number="24001",
            draw_date=datetime(2024, 1, 1),
            lottery_type=LOTTERY_TYPE_DLT,
            numbers=[1, 5, 15, 25, 35, 2, 8]
        )

        str_repr = str(data)
        assert "24001" in str_repr
        assert "2024-01-01" in str_repr
        assert "dlt" in str_repr

    def test_date_string_parsing(self):
        """测试日期字符串解析"""
        data = LotteryData(
            issue_number="24001",
            draw_date="2024-01-01",
            lottery_type=LOTTERY_TYPE_DLT,
            numbers=[1, 5, 15, 25, 35, 2, 8]
        )

        assert data.draw_date == datetime(2024, 1, 1)

    def test_empty_numbers(self):
        """测试空号码列表"""
        data = LotteryData(
            issue_number="24001",
            draw_date=datetime(2024, 1, 1),
            lottery_type=LOTTERY_TYPE_DLT,
            numbers=[]
        )

        assert data.numbers == []
        assert data.get_front_numbers() == []
        assert data.get_back_numbers() == []

    def test_case_insensitive_lottery_type(self):
        """测试彩票类型大小写不敏感"""
        data = LotteryData(
            issue_number="24001",
            draw_date=datetime(2024, 1, 1),
            lottery_type="DLT",
            numbers=[1, 5, 15, 25, 35, 2, 8]
        )

        assert data.lottery_type == "dlt"

    def test_invalid_date_format(self):
        """测试无效日期格式"""
        with pytest.raises(ValueError):
            LotteryData(
                issue_number="24001",
                draw_date="invalid-date",
                lottery_type=LOTTERY_TYPE_DLT,
                numbers=[1, 5, 15, 25, 35, 2, 8]
            )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])